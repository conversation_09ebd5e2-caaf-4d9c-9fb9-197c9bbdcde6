### Prepare Database
run `make migrateup` to migrate database and update schema

### card number format `0000-0000-0000-0000`

1. `iin` is a card issuer identifier number that is first `4` digits of card number.
- `0000`-0000-0000-0000
2. `card_type` is a card type that is `1` digit of second section of card number.
- 0000-`0`000-0000-0000
3. `account_number` is a card account number that is `10` digits long.
- 0000-0`000-0000-000`0
4. `luhn_digit` is a luhn digit that is `1` digit long.
- 0000-0000-0000-000`0`

### card expiration date format
### format:  `YY / MM`

card expiration date is a 4 digit number that is generated by concatenating the following:
- expire year (2 digits)
- expire month (2 digits)

### card cvv format `0000`
card cvv is a 4 digit number that is generated by concatenating the following:
- cvv (4 digits)
