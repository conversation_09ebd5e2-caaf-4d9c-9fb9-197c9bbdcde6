package ledger

import (
	"context"

	formancesdkgo "github.com/formancehq/formance-sdk-go/v3"
	"github.com/formancehq/formance-sdk-go/v3/pkg/models/operations"
	"github.com/formancehq/formance-sdk-go/v3/pkg/models/shared"
)

func CreateLedger(ctx context.Context, formance *formancesdkgo.Formance, name string, metadata map[string]string) (*operations.V2CreateLedgerResponse, error) {
	return formance.Ledger.V2.CreateLedger(ctx, operations.V2CreateLedgerRequest{
		V2CreateLedgerRequest: shared.V2CreateLedgerRequest{
			Metadata: metadata,
		},
		Ledger: name,
	})
}
