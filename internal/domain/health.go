package domain

import (
	"context"
	"time"
)

// HealthStatus represents the current health status of the service
type HealthStatus struct {
	Status    string    `json:"status"`
	Timestamp time.Time `json:"timestamp"`
	Details   struct {
		Database    string `json:"database"`
		Redis       string `json:"redis"`
		MongoDB     string `json:"mongodb"`
		MessageBus  string `json:"message_bus"`
		ServiceMesh string `json:"service_mesh"`
	} `json:"details"`
}

// HealthRepository defines the interface for health check operations
type HealthRepository interface {
	CheckDatabase(ctx context.Context) error
	CheckRedis(ctx context.Context) error
	CheckMongoDB(ctx context.Context) error
	CheckMessageBus(ctx context.Context) error
	CheckServiceMesh(ctx context.Context) error
}

// HealthService defines the interface for health service operations
type HealthService interface {
	Check(ctx context.Context) (*HealthStatus, error)
	Watch(ctx context.Context) (<-chan *HealthStatus, error)
}
