package config

// Config stores all configuration of the application.
// The values are read by viper from a config file or environment variable.
type Configuration struct {
	Environment                     string   `json:"environment" mapstructure:"ENVIRONMENT"`
	GrpcListenerHost                string   `json:"grpc_listener_host" mapstructure:"GRPC_LISTENER_HOST"`
	HttpListenerHost                string   `json:"http_listener_host" mapstructure:"HTTP_LISTENER_HOST"`
	DBDriver                        string   `json:"db_driver" mapstructure:"DB_DRIVER"`
	DBSource                        string   `json:"db_source" mapstructure:"DB_SOURCE"`
	DBMaxPoolSize                   int      `json:"db_max_pool_size" mapstructure:"DB_MAX_POOL_SIZE"`
	DBConnectionAttempt             int      `json:"db_connection_attempt" mapstructure:"DB_CONNECTION_ATTEMPT"`
	DBConnectionTimeout             int      `json:"db_connection_timeout" mapstructure:"DB_CONNECTION_TIMEOUT"`
	MigrationURL                    string   `json:"migration_url" mapstructure:"MIGRATION_URL"`
	NatsConnection                  string   `json:"nats_connection" mapstructure:"NATS_CONNECTION"`
	RedisHost                       string   `json:"redis_host" mapstructure:"REDIS_HOST"`
	RedisDB                         int      `json:"redis_db" mapstructure:"REDIS_DB"`
	RedisPassword                   string   `json:"redis_password" mapstructure:"REDIS_PASSWORD"`
	RefreshTokenDuration            int      `json:"refresh_token_duration" mapstructure:"REFRESH_TOKEN_DURATION"`
	AccessTokenDuration             int      `json:"access_token_duration" mapstructure:"ACCESS_TOKEN_DURATION"`
	VerificationDuration            int      `json:"verification_duration" mapstructure:"VERIFICATION_DURATION"`
	TokenSymmetricKey               string   `json:"token_symmetric_key" mapstructure:"TOKEN_SYMMETRIC_KEY"`
	Issuer                          string   `json:"issuer" mapstructure:"ISSUER"`
	Audience                        string   `json:"audience" mapstructure:"AUDIENCE"`
	LogsDestinationLabel            string   `json:"logs_destination_label" mapstructure:"LOGS_DESTINATION_LABEL"`
	LogsMongoUri                    string   `json:"logs_mongodb_uri" mapstructure:"LOGS_MONGODB_URI"`
	LogDestination                  string   `json:"log_destination" mapstructure:"LOG_DESTINATION"`
	DaprHost                        string   `json:"dapr_host" mapstructure:"DAPR_HOST"`
	DaprSecretStoreName             string   `json:"dapr_secret_store_name" mapstructure:"DAPR_SECRET_STORE_NAME"`
	HealthCheckKey                  string   `json:"health_check_key" mapstructure:"HEALTH_CHECK_KEY"`
	Roles                           []string `json:"roles" mapstructure:"ROLES"`
	SchemaPath                      string   `json:"schema_path" mapstructure:"SCHEMA_PATH"`
	JaegerHost                      string   `json:"jaeger_host" mapstructure:"JAEGER_HOST"`
	NotificationTopic               string   `json:"notification_topic" mapstructure:"NOTIFICATION_TOPIC"`
	DefaultCardExpireYears          int      `json:"default_card_expire_years" mapstructure:"DEFAULT_CARD_EXPIRE_YEARS"`
	DefaultNetworkCreditLedger      string   `json:"default_network_credit_ledger" mapstructure:"DEFAULT_NETWORK_CREDIT_LEDGER"`
	FormanceURL                     string   `json:"formance_url" mapstructure:"FORMANCE_URL"`
	DefaultCardCreditAccountPattern string   `json:"default_card_credit_account_pattern" mapstructure:"DEFAULT_CARD_CREDIT_ACCOUNT_PATTERN"`
}
