-- SQL dump generated using DBML (dbml.dbdiagram.io)
-- Database: PostgreSQL
-- Generated at: 2025-08-02T08:09:25.649Z

CREATE TYPE "order_status" AS ENUM (
  'BOOKED',
  'PAYMENT_PENDING',
  'PAYMENT_SETTLED',
  'PAYMENT_SUCCESS',
  'PAYMENT_FAILED',
  'USER_CANCELLED',
  'SYSTEM_CANCELLED',
  'EXPIRED'
);

CREATE TABLE "orders" (
  "id" BIGSERIAL UNIQUE PRIMARY KEY NOT NULL,
  "identifier" varchar(64) UNIQUE NOT NULL,
  "user_identifier" varchar(64) NOT NULL,
  "card_identifier" varchar(64) NOT NULL,
  "card_holder_name" varchar(256) NOT NULL,
  "card_pan" varchar(24) NOT NULL,
  "target_domain" varchar(256) NOT NULL,
  "status" order_status NOT NULL DEFAULT 'BOOKED',
  "order_type" varchar(256) NOT NULL,
  "order_amount" varchar(32) NOT NULL,
  "order_asset" varchar(32) NOT NULL,
  "order_description" varchar(256) NOT NULL,
  "reference" varchar(256) UNIQUE NOT NULL,
  "voucher_code" varchar(256) NOT NULL DEFAULT '',
  "voucher_discount_amount" varchar(32) NOT NULL DEFAULT '0',
  "meta_data" jsonb NOT NULL DEFAULT '{}',
  "created_at" timestamptz NOT NULL DEFAULT (now()),
  "updated_at" timestamptz,
  "deleted_at" timestamptz
);

CREATE TABLE "vouchers" (
  "id" BIGSERIAL UNIQUE PRIMARY KEY NOT NULL,
  "code" varchar(256) UNIQUE NOT NULL,
  "description" varchar(256) NOT NULL,
  "discount_percentage" varchar(32) NOT NULL DEFAULT '3',
  "asset" varchar(32) NOT NULL DEFAULT 'IRR',
  "per_user_max_usage" int NOT NULL DEFAULT 0,
  "expires_at" timestamptz,
  "created_at" timestamptz NOT NULL DEFAULT (now()),
  "updated_at" timestamptz,
  "deleted_at" timestamptz
);

COMMENT ON COLUMN "orders"."id" IS 'order internal system unique id';

COMMENT ON COLUMN "orders"."identifier" IS 'unique external identifier for internal-external system identifier isolation';

COMMENT ON COLUMN "orders"."user_identifier" IS 'customer user external identifier';

COMMENT ON COLUMN "orders"."card_identifier" IS 'card external identifier';

COMMENT ON COLUMN "orders"."card_holder_name" IS 'card holder name';

COMMENT ON COLUMN "orders"."card_pan" IS 'card pan number';

COMMENT ON COLUMN "orders"."target_domain" IS 'target domain for order';

COMMENT ON COLUMN "orders"."status" IS 'order status determinable value';

COMMENT ON COLUMN "orders"."order_type" IS 'order type';

COMMENT ON COLUMN "orders"."order_amount" IS 'order amount';

COMMENT ON COLUMN "orders"."order_asset" IS 'order asset';

COMMENT ON COLUMN "orders"."order_description" IS 'order description';

COMMENT ON COLUMN "orders"."reference" IS 'order reference to make it idempotent';

COMMENT ON COLUMN "orders"."voucher_code" IS 'voucher code for order';

COMMENT ON COLUMN "orders"."voucher_discount_amount" IS 'voucher discount amount';

COMMENT ON COLUMN "orders"."meta_data" IS 'order meta data';

COMMENT ON COLUMN "orders"."created_at" IS 'when order was created';

COMMENT ON COLUMN "orders"."updated_at" IS 'when order was updated';

COMMENT ON COLUMN "orders"."deleted_at" IS 'when order was deleted';

COMMENT ON COLUMN "vouchers"."id" IS 'voucher internal system unique id';

COMMENT ON COLUMN "vouchers"."code" IS 'voucher code';

COMMENT ON COLUMN "vouchers"."description" IS 'voucher description';

COMMENT ON COLUMN "vouchers"."discount_percentage" IS 'voucher discount percentage';

COMMENT ON COLUMN "vouchers"."asset" IS 'voucher asset';

COMMENT ON COLUMN "vouchers"."per_user_max_usage" IS 'per user max usage for voucher';

COMMENT ON COLUMN "vouchers"."expires_at" IS 'when voucher was expired';

COMMENT ON COLUMN "vouchers"."created_at" IS 'when voucher was created';

COMMENT ON COLUMN "vouchers"."updated_at" IS 'when voucher was updated';

COMMENT ON COLUMN "vouchers"."deleted_at" IS 'when voucher was deleted';
