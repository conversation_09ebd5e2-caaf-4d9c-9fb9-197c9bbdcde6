package postgres

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/liveutil/go-lib/card_iso"
	"github.com/liveutil/go-lib/stringutil"
	"github.com/oklog/ulid/v2"
)

type GenerateCardParams struct {
	UserIdentifier     string
	IinIdentifier      string
	CardTypeIdentifier string
	ExpiresAt          time.Time
	ExpireMonth        string
	ExpireYear         string
	Name               string
	Metadata           []byte
}

// CreateCardTxParams is the params for CreateCardTx.
type CreateCardTxParams struct {
	GenerateCardParams
	AfterCreate func(user Card) error
}

// CardTxResult is the result for CreateCardTx.
type CardTxResult struct {
	Card Card
}

// CreateCardTx creates a new card and then calls the AfterCreate function.
func (store *SQLStore) CreateCardTx(ctx context.Context, arg CreateCardTxParams) (CardTxResult, error) {
	var result CardTxResult

	err := store.execTx(ctx, func(q *Queries) error {
		var err error

		iin, err := q.GetIssuerIdentifierByIdentifier(ctx, GetIssuerIdentifierByIdentifierParams{
			Identifier: arg.IinIdentifier,
			Status:     IinStatusACTIVE,
		})
		if err != nil {
			return err
		}

		cardType, err := q.GetCardTypeByIdentifier(ctx, arg.CardTypeIdentifier)
		if err != nil {
			return err
		}

		lastCardAccountNumber := uint64(0)

		lastCard, err := q.GetLastAccountNumberForIinAndCardType(ctx, GetLastAccountNumberForIinAndCardTypeParams{
			Iin:      iin.ID,
			CardType: cardType.ID,
		})
		if err == nil {
			lastCardAccountNumber, err = strconv.ParseUint(lastCard.AccountNumber, 10, 64)
			if err != nil {
				return err
			}
		}

		if err != nil && !errors.Is(err, pgx.ErrNoRows) {
			return err
		}

		lastCardAccountNumber++

		pan, err := card_iso.GenerateCard(
			iin.Iin,
			lastCardAccountNumber,
			1,
			fmt.Sprintf("%s%s", arg.ExpireMonth, arg.ExpireYear),
			arg.Name,
			iin.DesKey,
			"",
		)
		if err != nil {
			return err
		}

		if len(pan) != 1 {
			return errors.New("card PAN generation failed")
		}

		pinCode, err := stringutil.RandDigits(4)
		if err != nil {
			return err
		}

		pinCodeBcrypt, err := stringutil.HashString(pinCode)
		if err != nil {
			return err
		}

		result.Card, err = q.CreateCard(ctx, CreateCardParams{
			Identifier:           ulid.Make().String(),
			UserIdentifier:       arg.UserIdentifier,
			Status:               CardStatusISSUED,
			Iin:                  iin.ID,
			IinCode:              iin.Iin,
			CardType:             cardType.ID,
			CardTypeCode:         cardType.Code,
			AccountNumber:        fmt.Sprintf("%010d", lastCardAccountNumber),
			LuhnDigit:            pan[0].PAN[15:16],
			PinCode:              pinCodeBcrypt,
			PrimaryAccountNumber: pan[0].PAN,
			ExpireYear:           pan[0].ExpiryMMYY[2:4],
			ExpireMonth:          pan[0].ExpiryMMYY[0:2],
			MetaData:             arg.Metadata,
			ExpiresAt: pgtype.Timestamptz{
				Time:  arg.ExpiresAt,
				Valid: true,
			},
			Cvv1:                         pan[0].CVV1,
			Cvv2:                         pan[0].CVV2,
			Icvv:                         pan[0].ICVV,
			Track1:                       pan[0].Track1,
			Track2:                       pan[0].Track2,
			ApplicationProtocolDataUnits: pan[0].APDUs,
			EmvXml:                       pan[0].EMVXML,
		})
		if err != nil {
			return err
		}

		return arg.AfterCreate(result.Card)
	})

	return result, err
}
