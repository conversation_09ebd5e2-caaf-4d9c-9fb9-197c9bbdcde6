// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: issuer_identifiers.sql

package postgres

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const createIssuerIdentifier = `-- name: CreateIssuerIdentifier :one
INSERT INTO issuer_identifiers (
  identifier,
  iin,
  status,
  issuer_user_identifier,
  issuer_name,
  issuer_logo,
  issuer_url,
  meta_data,
  expires_at,
  updated_at,
  des_key
) VALUES (
  $1, $2, $3, $4, $5, $6, $7, $8, $9, CURRENT_TIMESTAMP, $10
)
RETURNING id, identifier, iin, status, issuer_user_identifier, issuer_name, issuer_logo, issuer_url, des_key, is_default, meta_data, expires_at, created_at, updated_at, deleted_at
`

type CreateIssuerIdentifierParams struct {
	Identifier           string             `json:"identifier"`
	Iin                  string             `json:"iin"`
	Status               IinStatus          `json:"status"`
	IssuerUserIdentifier string             `json:"issuer_user_identifier"`
	IssuerName           string             `json:"issuer_name"`
	IssuerLogo           pgtype.Text        `json:"issuer_logo"`
	IssuerUrl            pgtype.Text        `json:"issuer_url"`
	MetaData             []byte             `json:"meta_data"`
	ExpiresAt            pgtype.Timestamptz `json:"expires_at"`
	DesKey               []byte             `json:"des_key"`
}

func (q *Queries) CreateIssuerIdentifier(ctx context.Context, arg CreateIssuerIdentifierParams) (IssuerIdentifier, error) {
	row := q.db.QueryRow(ctx, createIssuerIdentifier,
		arg.Identifier,
		arg.Iin,
		arg.Status,
		arg.IssuerUserIdentifier,
		arg.IssuerName,
		arg.IssuerLogo,
		arg.IssuerUrl,
		arg.MetaData,
		arg.ExpiresAt,
		arg.DesKey,
	)
	var i IssuerIdentifier
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Iin,
		&i.Status,
		&i.IssuerUserIdentifier,
		&i.IssuerName,
		&i.IssuerLogo,
		&i.IssuerUrl,
		&i.DesKey,
		&i.IsDefault,
		&i.MetaData,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getDefaultIssuerIdentifier = `-- name: GetDefaultIssuerIdentifier :one
SELECT id, identifier, iin, status, issuer_user_identifier, issuer_name, issuer_logo, issuer_url, des_key, is_default, meta_data, expires_at, created_at, updated_at, deleted_at
FROM issuer_identifiers
WHERE is_default = true
    AND status = 'ACTIVE'
    AND deleted_at IS NULL
LIMIT 1
`

func (q *Queries) GetDefaultIssuerIdentifier(ctx context.Context) (IssuerIdentifier, error) {
	row := q.db.QueryRow(ctx, getDefaultIssuerIdentifier)
	var i IssuerIdentifier
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Iin,
		&i.Status,
		&i.IssuerUserIdentifier,
		&i.IssuerName,
		&i.IssuerLogo,
		&i.IssuerUrl,
		&i.DesKey,
		&i.IsDefault,
		&i.MetaData,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getIssuerIdentifierByID = `-- name: GetIssuerIdentifierByID :one
SELECT id, identifier, iin, status, issuer_user_identifier, issuer_name, issuer_logo, issuer_url, des_key, is_default, meta_data, expires_at, created_at, updated_at, deleted_at
FROM issuer_identifiers
WHERE id = $1
    AND status = $2
    AND deleted_at IS NULL
LIMIT 1
`

type GetIssuerIdentifierByIDParams struct {
	ID     int64     `json:"id"`
	Status IinStatus `json:"status"`
}

func (q *Queries) GetIssuerIdentifierByID(ctx context.Context, arg GetIssuerIdentifierByIDParams) (IssuerIdentifier, error) {
	row := q.db.QueryRow(ctx, getIssuerIdentifierByID, arg.ID, arg.Status)
	var i IssuerIdentifier
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Iin,
		&i.Status,
		&i.IssuerUserIdentifier,
		&i.IssuerName,
		&i.IssuerLogo,
		&i.IssuerUrl,
		&i.DesKey,
		&i.IsDefault,
		&i.MetaData,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getIssuerIdentifierByIdentifier = `-- name: GetIssuerIdentifierByIdentifier :one
SELECT id, identifier, iin, status, issuer_user_identifier, issuer_name, issuer_logo, issuer_url, des_key, is_default, meta_data, expires_at, created_at, updated_at, deleted_at
FROM issuer_identifiers
WHERE identifier = $1
    AND status = $2
    AND deleted_at IS NULL
LIMIT 1
`

type GetIssuerIdentifierByIdentifierParams struct {
	Identifier string    `json:"identifier"`
	Status     IinStatus `json:"status"`
}

func (q *Queries) GetIssuerIdentifierByIdentifier(ctx context.Context, arg GetIssuerIdentifierByIdentifierParams) (IssuerIdentifier, error) {
	row := q.db.QueryRow(ctx, getIssuerIdentifierByIdentifier, arg.Identifier, arg.Status)
	var i IssuerIdentifier
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Iin,
		&i.Status,
		&i.IssuerUserIdentifier,
		&i.IssuerName,
		&i.IssuerLogo,
		&i.IssuerUrl,
		&i.DesKey,
		&i.IsDefault,
		&i.MetaData,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getIssuerIdentifierByNumber = `-- name: GetIssuerIdentifierByNumber :one
SELECT id, identifier, iin, status, issuer_user_identifier, issuer_name, issuer_logo, issuer_url, des_key, is_default, meta_data, expires_at, created_at, updated_at, deleted_at
FROM issuer_identifiers
WHERE iin = $1
    AND deleted_at IS NULL
LIMIT 1
`

func (q *Queries) GetIssuerIdentifierByNumber(ctx context.Context, iin string) (IssuerIdentifier, error) {
	row := q.db.QueryRow(ctx, getIssuerIdentifierByNumber, iin)
	var i IssuerIdentifier
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Iin,
		&i.Status,
		&i.IssuerUserIdentifier,
		&i.IssuerName,
		&i.IssuerLogo,
		&i.IssuerUrl,
		&i.DesKey,
		&i.IsDefault,
		&i.MetaData,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getIssuerIdentifierByNumberAndStatus = `-- name: GetIssuerIdentifierByNumberAndStatus :one
SELECT id, identifier, iin, status, issuer_user_identifier, issuer_name, issuer_logo, issuer_url, des_key, is_default, meta_data, expires_at, created_at, updated_at, deleted_at
FROM issuer_identifiers
WHERE iin = $1
    AND status = $2
    AND deleted_at IS NULL
LIMIT 1
`

type GetIssuerIdentifierByNumberAndStatusParams struct {
	Iin    string    `json:"iin"`
	Status IinStatus `json:"status"`
}

func (q *Queries) GetIssuerIdentifierByNumberAndStatus(ctx context.Context, arg GetIssuerIdentifierByNumberAndStatusParams) (IssuerIdentifier, error) {
	row := q.db.QueryRow(ctx, getIssuerIdentifierByNumberAndStatus, arg.Iin, arg.Status)
	var i IssuerIdentifier
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Iin,
		&i.Status,
		&i.IssuerUserIdentifier,
		&i.IssuerName,
		&i.IssuerLogo,
		&i.IssuerUrl,
		&i.DesKey,
		&i.IsDefault,
		&i.MetaData,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getIssuerIdentifierByUserIdentifier = `-- name: GetIssuerIdentifierByUserIdentifier :one
SELECT id, identifier, iin, status, issuer_user_identifier, issuer_name, issuer_logo, issuer_url, des_key, is_default, meta_data, expires_at, created_at, updated_at, deleted_at
FROM issuer_identifiers
WHERE issuer_user_identifier = $1
    AND deleted_at IS NULL
LIMIT 1
`

func (q *Queries) GetIssuerIdentifierByUserIdentifier(ctx context.Context, issuerUserIdentifier string) (IssuerIdentifier, error) {
	row := q.db.QueryRow(ctx, getIssuerIdentifierByUserIdentifier, issuerUserIdentifier)
	var i IssuerIdentifier
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Iin,
		&i.Status,
		&i.IssuerUserIdentifier,
		&i.IssuerName,
		&i.IssuerLogo,
		&i.IssuerUrl,
		&i.DesKey,
		&i.IsDefault,
		&i.MetaData,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const updateIssuerIdentifier = `-- name: UpdateIssuerIdentifier :one
UPDATE issuer_identifiers
SET
  status = $2,
  issuer_name = $3,
  issuer_logo = $4,
  issuer_url = $5,
  meta_data = $6,
  expires_at = $7,
  updated_at = CURRENT_TIMESTAMP
WHERE identifier = $1
    AND deleted_at IS NULL
RETURNING id, identifier, iin, status, issuer_user_identifier, issuer_name, issuer_logo, issuer_url, des_key, is_default, meta_data, expires_at, created_at, updated_at, deleted_at
`

type UpdateIssuerIdentifierParams struct {
	Identifier string             `json:"identifier"`
	Status     IinStatus          `json:"status"`
	IssuerName string             `json:"issuer_name"`
	IssuerLogo pgtype.Text        `json:"issuer_logo"`
	IssuerUrl  pgtype.Text        `json:"issuer_url"`
	MetaData   []byte             `json:"meta_data"`
	ExpiresAt  pgtype.Timestamptz `json:"expires_at"`
}

func (q *Queries) UpdateIssuerIdentifier(ctx context.Context, arg UpdateIssuerIdentifierParams) (IssuerIdentifier, error) {
	row := q.db.QueryRow(ctx, updateIssuerIdentifier,
		arg.Identifier,
		arg.Status,
		arg.IssuerName,
		arg.IssuerLogo,
		arg.IssuerUrl,
		arg.MetaData,
		arg.ExpiresAt,
	)
	var i IssuerIdentifier
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Iin,
		&i.Status,
		&i.IssuerUserIdentifier,
		&i.IssuerName,
		&i.IssuerLogo,
		&i.IssuerUrl,
		&i.DesKey,
		&i.IsDefault,
		&i.MetaData,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}
