// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package postgres

import (
	"database/sql/driver"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

type OrderStatus string

const (
	OrderStatusBOOKED          OrderStatus = "BOOKED"
	OrderStatusPAYMENTPENDING  OrderStatus = "PAYMENT_PENDING"
	OrderStatusPAYMENTSETTLED  OrderStatus = "PAYMENT_SETTLED"
	OrderStatusPAYMENTSUCCESS  OrderStatus = "PAYMENT_SUCCESS"
	OrderStatusPAYMENTFAILED   OrderStatus = "PAYMENT_FAILED"
	OrderStatusUSERCANCELLED   OrderStatus = "USER_CANCELLED"
	OrderStatusSYSTEMCANCELLED OrderStatus = "SYSTEM_CANCELLED"
	OrderStatusEXPIRED         OrderStatus = "EXPIRED"
)

func (e *OrderStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = OrderStatus(s)
	case string:
		*e = OrderStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for OrderStatus: %T", src)
	}
	return nil
}

type NullOrderStatus struct {
	OrderStatus OrderStatus `json:"order_status"`
	Valid       bool        `json:"valid"` // Valid is true if OrderStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullOrderStatus) Scan(value interface{}) error {
	if value == nil {
		ns.OrderStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.OrderStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullOrderStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.OrderStatus), nil
}

type Order struct {
	// order internal system unique id
	ID int64 `json:"id"`
	// unique external identifier for internal-external system identifier isolation
	Identifier string `json:"identifier"`
	// customer user external identifier
	UserIdentifier string `json:"user_identifier"`
	// card external identifier
	CardIdentifier string `json:"card_identifier"`
	// card holder name
	CardHolderName string `json:"card_holder_name"`
	// card pan number
	CardPan string `json:"card_pan"`
	// target domain for order
	TargetDomain string `json:"target_domain"`
	// order status determinable value
	Status OrderStatus `json:"status"`
	// order type
	OrderType string `json:"order_type"`
	// order amount
	OrderAmount string `json:"order_amount"`
	// order asset
	OrderAsset string `json:"order_asset"`
	// order description
	OrderDescription string `json:"order_description"`
	// order reference to make it idempotent
	Reference string `json:"reference"`
	// voucher code for order
	VoucherCode string `json:"voucher_code"`
	// voucher discount amount
	VoucherDiscountAmount string `json:"voucher_discount_amount"`
	// order meta data
	MetaData []byte `json:"meta_data"`
	// when order was created
	CreatedAt time.Time `json:"created_at"`
	// when order was updated
	UpdatedAt pgtype.Timestamptz `json:"updated_at"`
	// when order was deleted
	DeletedAt pgtype.Timestamptz `json:"deleted_at"`
}

type Voucher struct {
	// voucher internal system unique id
	ID int64 `json:"id"`
	// voucher code
	Code string `json:"code"`
	// voucher description
	Description string `json:"description"`
	// voucher discount percentage
	DiscountPercentage string `json:"discount_percentage"`
	// voucher asset
	Asset string `json:"asset"`
	// per user max usage for voucher
	PerUserMaxUsage int32 `json:"per_user_max_usage"`
	// when voucher was expired
	ExpiresAt pgtype.Timestamptz `json:"expires_at"`
	// when voucher was created
	CreatedAt time.Time `json:"created_at"`
	// when voucher was updated
	UpdatedAt pgtype.Timestamptz `json:"updated_at"`
	// when voucher was deleted
	DeletedAt pgtype.Timestamptz `json:"deleted_at"`
}
