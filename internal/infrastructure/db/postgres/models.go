// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package postgres

import (
	"database/sql/driver"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

type CardStatus string

const (
	CardStatusISSUING         CardStatus = "ISSUING"
	CardStatusISSUED          CardStatus = "ISSUED"
	CardStatusDELIVERED       CardStatus = "DELIVERED"
	CardStatusLEGALLOCKED     CardStatus = "LEGAL_LOCKED"
	CardStatusPINLOCKED       CardStatus = "PIN_LOCKED"
	CardStatusUSERLOCKED      CardStatus = "USER_LOCKED"
	CardStatusEXPIREDLOCKED   CardStatus = "EXPIRED_LOCKED"
	CardStatusACTIVE          CardStatus = "ACTIVE"
	CardStatusSUSPENDED       CardStatus = "SUSPENDED"
	CardStatusLOST            CardStatus = "LOST"
	CardStatusSTOLEN          CardStatus = "STOLEN"
	CardStatusREPLACEMENT     CardStatus = "REPLACEMENT"
	CardStatusREPLACEDLOCKED  CardStatus = "REPLACED_LOCKED"
	CardStatusREPLACEDEXPIRED CardStatus = "REPLACED_EXPIRED"
	CardStatusREPLACEDBLOCKED CardStatus = "REPLACED_BLOCKED"
	CardStatusREPLACEDDELETED CardStatus = "REPLACED_DELETED"
	CardStatusREPLACED        CardStatus = "REPLACED"
	CardStatusEXPIRED         CardStatus = "EXPIRED"
	CardStatusBLOCKED         CardStatus = "BLOCKED"
	CardStatusDELETED         CardStatus = "DELETED"
)

func (e *CardStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = CardStatus(s)
	case string:
		*e = CardStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for CardStatus: %T", src)
	}
	return nil
}

type NullCardStatus struct {
	CardStatus CardStatus `json:"card_status"`
	Valid      bool       `json:"valid"` // Valid is true if CardStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullCardStatus) Scan(value interface{}) error {
	if value == nil {
		ns.CardStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.CardStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullCardStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.CardStatus), nil
}

type IinStatus string

const (
	IinStatusPENDING     IinStatus = "PENDING"
	IinStatusREJECTED    IinStatus = "REJECTED"
	IinStatusACTIVE      IinStatus = "ACTIVE"
	IinStatusINACTIVE    IinStatus = "INACTIVE"
	IinStatusDELETED     IinStatus = "DELETED"
	IinStatusBLOCKED     IinStatus = "BLOCKED"
	IinStatusSUSPENDED   IinStatus = "SUSPENDED"
	IinStatusLEGALLOCKED IinStatus = "LEGAL_LOCKED"
)

func (e *IinStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = IinStatus(s)
	case string:
		*e = IinStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for IinStatus: %T", src)
	}
	return nil
}

type NullIinStatus struct {
	IinStatus IinStatus `json:"iin_status"`
	Valid     bool      `json:"valid"` // Valid is true if IinStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullIinStatus) Scan(value interface{}) error {
	if value == nil {
		ns.IinStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.IinStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullIinStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.IinStatus), nil
}

type Card struct {
	// card internal system unique id
	ID int64 `json:"id"`
	// unique external identifier for internal-external system identifier isolation
	Identifier string `json:"identifier"`
	// owner of card external identifier
	UserIdentifier string `json:"user_identifier"`
	// card status determinable value
	Status CardStatus `json:"status"`
	// card issuer identifier id
	Iin int64 `json:"iin"`
	// card type id
	CardType int64 `json:"card_type"`
	// is this card default card for user or not
	IsUserDefaultCard bool `json:"is_user_default_card"`
	// card issuer identifier code number
	IinCode string `json:"iin_code"`
	// card / service or issuer defined card type code
	CardTypeCode string `json:"card_type_code"`
	// card owner account number
	AccountNumber string `json:"account_number"`
	// Luhn Checksum digit for given (iin + account_number) string
	LuhnDigit string `json:"luhn_digit"`
	// CVV1 for magnetic stripe (service code 101)
	Cvv1 string `json:"cvv1"`
	// iCVV for chip cards (service code 201)
	Icvv string `json:"icvv"`
	// CVV2 for card-not-present transactions (service code 999)
	Cvv2 string `json:"cvv2"`
	// track 1 data for magnetic stripe cards
	Track1 string `json:"track_1"`
	// track 2 data for magnetic stripe cards
	Track2 string `json:"track_2"`
	// application protocol data units for chip cards
	ApplicationProtocolDataUnits []string `json:"application_protocol_data_units"`
	// EMV profile in XML format
	EmvXml string `json:"emv_xml"`
	// bcrypted random unique 4 or 6 digit pin code number that used for signing transactions
	PinCode string `json:"pin_code"`
	// complete card number string using [iin(4) + account_number(11) + luhn_checksum(1) = 16 character
	PrimaryAccountNumber string `json:"primary_account_number"`
	// issued card valid before this year
	ExpireYear string `json:"expire_year"`
	// issued card valid before this year / month
	ExpireMonth string `json:"expire_month"`
	// card metadatas
	MetaData []byte `json:"meta_data"`
	// when the card will expires
	ExpiresAt pgtype.Timestamptz `json:"expires_at"`
	// when card was created
	CreatedAt time.Time `json:"created_at"`
	// when card was updated
	UpdatedAt pgtype.Timestamptz `json:"updated_at"`
	// when card was deleted
	DeletedAt pgtype.Timestamptz `json:"deleted_at"`
}

type CardType struct {
	// card type internal system unique id
	ID int64 `json:"id"`
	// unique external identifier for internal-external system identifier isolation
	Identifier string `json:"identifier"`
	// card type code
	Code string `json:"code"`
	// card type name
	Name string `json:"name"`
	// card type description
	Description pgtype.Text `json:"description"`
	// card type metadatas
	MetaData []byte `json:"meta_data"`
	// is this card type default type for creating signed up users default card or not
	IsDefault bool `json:"is_default"`
	// card issuer identifier id
	Iin int64 `json:"iin"`
	// when card type was created
	CreatedAt time.Time `json:"created_at"`
	// when card type was updated
	UpdatedAt pgtype.Timestamptz `json:"updated_at"`
	// when card type was deleted
	DeletedAt pgtype.Timestamptz `json:"deleted_at"`
}

type IssuerIdentifier struct {
	// iin registry internal system unique id
	ID int64 `json:"id"`
	// unique external identifier for internal-external system identifier isolation
	Identifier string `json:"identifier"`
	// card issuer identifier number
	Iin string `json:"iin"`
	// iin registry status determinable value
	Status IinStatus `json:"status"`
	// issuer owner user external identifier
	IssuerUserIdentifier string `json:"issuer_user_identifier"`
	// issuer name
	IssuerName string `json:"issuer_name"`
	// issuer logo url
	IssuerLogo pgtype.Text `json:"issuer_logo"`
	// issuer website url
	IssuerUrl pgtype.Text `json:"issuer_url"`
	// 3DES key for encrypting card data
	DesKey []byte `json:"des_key"`
	// is this iin default issuer for creating signed up users default card or not
	IsDefault bool `json:"is_default"`
	// iin registry metadatas
	MetaData []byte `json:"meta_data"`
	// when the iin registry will expires
	ExpiresAt pgtype.Timestamptz `json:"expires_at"`
	// when iin registry was created
	CreatedAt time.Time `json:"created_at"`
	// when iin registry was updated
	UpdatedAt pgtype.Timestamptz `json:"updated_at"`
	// when iin registry was deleted
	DeletedAt pgtype.Timestamptz `json:"deleted_at"`
}
