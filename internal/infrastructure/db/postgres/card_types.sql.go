// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: card_types.sql

package postgres

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const createCardType = `-- name: CreateCardType :one
INSERT INTO card_types (
  identifier,
  code,
  name,
  description,
  iin,
  meta_data,
  created_at,
  updated_at
) VALUES (
  $1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
) RETURNING id, identifier, code, name, description, meta_data, is_default, iin, created_at, updated_at, deleted_at
`

type CreateCardTypeParams struct {
	Identifier  string      `json:"identifier"`
	Code        string      `json:"code"`
	Name        string      `json:"name"`
	Description pgtype.Text `json:"description"`
	Iin         int64       `json:"iin"`
	MetaData    []byte      `json:"meta_data"`
}

func (q *Queries) CreateCardType(ctx context.Context, arg CreateCardTypeParams) (CardType, error) {
	row := q.db.QueryRow(ctx, createCardType,
		arg.Identifier,
		arg.Code,
		arg.Name,
		arg.Description,
		arg.Iin,
		arg.MetaData,
	)
	var i CardType
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Code,
		&i.Name,
		&i.Description,
		&i.MetaData,
		&i.IsDefault,
		&i.Iin,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getCardTypeByCode = `-- name: GetCardTypeByCode :one
SELECT id, identifier, code, name, description, meta_data, is_default, iin, created_at, updated_at, deleted_at
FROM card_types
WHERE code = $1
    AND deleted_at IS NULL
LIMIT 1
`

func (q *Queries) GetCardTypeByCode(ctx context.Context, code string) (CardType, error) {
	row := q.db.QueryRow(ctx, getCardTypeByCode, code)
	var i CardType
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Code,
		&i.Name,
		&i.Description,
		&i.MetaData,
		&i.IsDefault,
		&i.Iin,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getCardTypeByIdentifier = `-- name: GetCardTypeByIdentifier :one
SELECT id, identifier, code, name, description, meta_data, is_default, iin, created_at, updated_at, deleted_at
FROM card_types
WHERE identifier = $1
    AND deleted_at IS NULL
LIMIT 1
`

func (q *Queries) GetCardTypeByIdentifier(ctx context.Context, identifier string) (CardType, error) {
	row := q.db.QueryRow(ctx, getCardTypeByIdentifier, identifier)
	var i CardType
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Code,
		&i.Name,
		&i.Description,
		&i.MetaData,
		&i.IsDefault,
		&i.Iin,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getDefaultCardType = `-- name: GetDefaultCardType :one
SELECT id, identifier, code, name, description, meta_data, is_default, iin, created_at, updated_at, deleted_at
FROM card_types
WHERE is_default = true
    AND deleted_at IS NULL
LIMIT 1
`

func (q *Queries) GetDefaultCardType(ctx context.Context) (CardType, error) {
	row := q.db.QueryRow(ctx, getDefaultCardType)
	var i CardType
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Code,
		&i.Name,
		&i.Description,
		&i.MetaData,
		&i.IsDefault,
		&i.Iin,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const updateCardType = `-- name: UpdateCardType :one
UPDATE card_types
SET
  name = $2,
  description = $3,
  meta_data = $4,
  updated_at = CURRENT_TIMESTAMP
WHERE code = $1
  AND deleted_at IS NULL
RETURNING id, identifier, code, name, description, meta_data, is_default, iin, created_at, updated_at, deleted_at
`

type UpdateCardTypeParams struct {
	Code        string      `json:"code"`
	Name        string      `json:"name"`
	Description pgtype.Text `json:"description"`
	MetaData    []byte      `json:"meta_data"`
}

func (q *Queries) UpdateCardType(ctx context.Context, arg UpdateCardTypeParams) (CardType, error) {
	row := q.db.QueryRow(ctx, updateCardType,
		arg.Code,
		arg.Name,
		arg.Description,
		arg.MetaData,
	)
	var i CardType
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.Code,
		&i.Name,
		&i.Description,
		&i.MetaData,
		&i.IsDefault,
		&i.Iin,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}
