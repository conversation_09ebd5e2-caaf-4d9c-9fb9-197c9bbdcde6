// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: vouchers.sql

package postgres

import (
	"context"
)

const getVoucherByCode = `-- name: GetVoucherByCode :one
SELECT id, code, description, discount_percentage, asset, per_user_max_usage, expires_at, created_at, updated_at, deleted_at
FROM vouchers
WHERE code = $1
    AND deleted_at IS NULL
    AND expires_at > CURRENT_TIMESTAMP
LIMIT 1
`

func (q *Queries) GetVoucherByCode(ctx context.Context, code string) (Voucher, error) {
	row := q.db.QueryRow(ctx, getVoucherByCode, code)
	var i Voucher
	err := row.Scan(
		&i.ID,
		&i.Code,
		&i.Description,
		&i.DiscountPercentage,
		&i.Asset,
		&i.PerUserMaxUsage,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}
