// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: orders.sql

package postgres

import (
	"context"
)

const countUserUsedVoucher = `-- name: CountUserUsedVoucher :one
SELECT COUNT(*)
FROM orders
WHERE voucher_code = $1
    AND user_identifier = $2
    AND deleted_at IS NULL
`

type CountUserUsedVoucherParams struct {
	VoucherCode    string `json:"voucher_code"`
	UserIdentifier string `json:"user_identifier"`
}

func (q *Queries) CountUserUsedVoucher(ctx context.Context, arg CountUserUsedVoucherParams) (int64, error) {
	row := q.db.QueryRow(ctx, countUserUsedVoucher, arg.VoucherCode, arg.UserIdentifier)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const getOrderByReference = `-- name: GetOrderByReference :one
SELECT id, identifier, user_identifier, card_identifier, card_holder_name, card_pan, target_domain, status, order_type, order_amount, order_asset, order_description, reference, voucher_code, voucher_discount_amount, meta_data, created_at, updated_at, deleted_at
FROM orders
WHERE reference = $1
    AND deleted_at IS NULL
LIMIT 1
`

func (q *Queries) GetOrderByReference(ctx context.Context, reference string) (Order, error) {
	row := q.db.QueryRow(ctx, getOrderByReference, reference)
	var i Order
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.UserIdentifier,
		&i.CardIdentifier,
		&i.CardHolderName,
		&i.CardPan,
		&i.TargetDomain,
		&i.Status,
		&i.OrderType,
		&i.OrderAmount,
		&i.OrderAsset,
		&i.OrderDescription,
		&i.Reference,
		&i.VoucherCode,
		&i.VoucherDiscountAmount,
		&i.MetaData,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getUserOrderByReference = `-- name: GetUserOrderByReference :one
SELECT id, identifier, user_identifier, card_identifier, card_holder_name, card_pan, target_domain, status, order_type, order_amount, order_asset, order_description, reference, voucher_code, voucher_discount_amount, meta_data, created_at, updated_at, deleted_at
FROM orders
WHERE reference = $1
    AND user_identifier = $2
    AND deleted_at IS NULL
LIMIT 1
`

type GetUserOrderByReferenceParams struct {
	Reference      string `json:"reference"`
	UserIdentifier string `json:"user_identifier"`
}

func (q *Queries) GetUserOrderByReference(ctx context.Context, arg GetUserOrderByReferenceParams) (Order, error) {
	row := q.db.QueryRow(ctx, getUserOrderByReference, arg.Reference, arg.UserIdentifier)
	var i Order
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.UserIdentifier,
		&i.CardIdentifier,
		&i.CardHolderName,
		&i.CardPan,
		&i.TargetDomain,
		&i.Status,
		&i.OrderType,
		&i.OrderAmount,
		&i.OrderAsset,
		&i.OrderDescription,
		&i.Reference,
		&i.VoucherCode,
		&i.VoucherDiscountAmount,
		&i.MetaData,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const listUserOrders = `-- name: ListUserOrders :many
SELECT id, identifier, user_identifier, card_identifier, card_holder_name, card_pan, target_domain, status, order_type, order_amount, order_asset, order_description, reference, voucher_code, voucher_discount_amount, meta_data, created_at, updated_at, deleted_at
FROM orders
WHERE user_identifier = $1
    AND deleted_at IS NULL
ORDER BY created_at DESC
LIMIT $2 OFFSET $3
`

type ListUserOrdersParams struct {
	UserIdentifier string `json:"user_identifier"`
	Limit          int32  `json:"limit"`
	Offset         int32  `json:"offset"`
}

func (q *Queries) ListUserOrders(ctx context.Context, arg ListUserOrdersParams) ([]Order, error) {
	rows, err := q.db.Query(ctx, listUserOrders, arg.UserIdentifier, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Order{}
	for rows.Next() {
		var i Order
		if err := rows.Scan(
			&i.ID,
			&i.Identifier,
			&i.UserIdentifier,
			&i.CardIdentifier,
			&i.CardHolderName,
			&i.CardPan,
			&i.TargetDomain,
			&i.Status,
			&i.OrderType,
			&i.OrderAmount,
			&i.OrderAsset,
			&i.OrderDescription,
			&i.Reference,
			&i.VoucherCode,
			&i.VoucherDiscountAmount,
			&i.MetaData,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const setOrderStatus = `-- name: SetOrderStatus :exec
UPDATE orders
SET status = $1,
    updated_at = CURRENT_TIMESTAMP
WHERE reference = $2
    AND deleted_at IS NULL
RETURNING id, identifier, user_identifier, card_identifier, card_holder_name, card_pan, target_domain, status, order_type, order_amount, order_asset, order_description, reference, voucher_code, voucher_discount_amount, meta_data, created_at, updated_at, deleted_at
`

type SetOrderStatusParams struct {
	Status    OrderStatus `json:"status"`
	Reference string      `json:"reference"`
}

func (q *Queries) SetOrderStatus(ctx context.Context, arg SetOrderStatusParams) error {
	_, err := q.db.Exec(ctx, setOrderStatus, arg.Status, arg.Reference)
	return err
}

const softDeleteOrder = `-- name: SoftDeleteOrder :exec
UPDATE orders
SET deleted_at = CURRENT_TIMESTAMP,
    updated_at = CURRENT_TIMESTAMP
WHERE reference = $1
    AND deleted_at IS NULL
RETURNING id, identifier, user_identifier, card_identifier, card_holder_name, card_pan, target_domain, status, order_type, order_amount, order_asset, order_description, reference, voucher_code, voucher_discount_amount, meta_data, created_at, updated_at, deleted_at
`

func (q *Queries) SoftDeleteOrder(ctx context.Context, reference string) error {
	_, err := q.db.Exec(ctx, softDeleteOrder, reference)
	return err
}

const upsertNewOrder = `-- name: UpsertNewOrder :one
INSERT INTO orders (
        identifier,
        reference,
        user_identifier,
        card_identifier,
        card_holder_name,
        card_pan,
        target_domain,
        status,
        order_type,
        order_amount,
        order_asset,
        order_description,
        reference,
        voucher_code,
        meta_data,
        created_at,
        updated_at
    )
VALUES (
        $1,
        $2,
        $3,
        $4,
        $5,
        $6,
        $7,
        $8,
        $9,
        $10,
        $11,
        $12,
        $13,
        $14,
        $15,
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP
    ) ON CONFLICT (reference) DO
UPDATE
SET user_identifier = EXCLUDED.user_identifier,
    card_identifier = EXCLUDED.card_identifier,
    card_holder_name = EXCLUDED.card_holder_name,
    card_pan = EXCLUDED.card_pan,
    target_domain = EXCLUDED.target_domain,
    status = EXCLUDED.status,
    order_type = EXCLUDED.order_type,
    order_amount = EXCLUDED.order_amount,
    order_asset = EXCLUDED.order_asset,
    order_description = EXCLUDED.order_description,
    voucher_code = EXCLUDED.voucher_code,
    meta_data = EXCLUDED.meta_data
WHERE orders.deleted_at IS NULL
RETURNING id, identifier, user_identifier, card_identifier, card_holder_name, card_pan, target_domain, status, order_type, order_amount, order_asset, order_description, reference, voucher_code, voucher_discount_amount, meta_data, created_at, updated_at, deleted_at
`

type UpsertNewOrderParams struct {
	Identifier       string      `json:"identifier"`
	Reference        string      `json:"reference"`
	UserIdentifier   string      `json:"user_identifier"`
	CardIdentifier   string      `json:"card_identifier"`
	CardHolderName   string      `json:"card_holder_name"`
	CardPan          string      `json:"card_pan"`
	TargetDomain     string      `json:"target_domain"`
	Status           OrderStatus `json:"status"`
	OrderType        string      `json:"order_type"`
	OrderAmount      string      `json:"order_amount"`
	OrderAsset       string      `json:"order_asset"`
	OrderDescription string      `json:"order_description"`
	Reference_2      string      `json:"reference_2"`
	VoucherCode      string      `json:"voucher_code"`
	MetaData         []byte      `json:"meta_data"`
}

// insert new order to database or update existing order params where reference is same
func (q *Queries) UpsertNewOrder(ctx context.Context, arg UpsertNewOrderParams) (Order, error) {
	row := q.db.QueryRow(ctx, upsertNewOrder,
		arg.Identifier,
		arg.Reference,
		arg.UserIdentifier,
		arg.CardIdentifier,
		arg.CardHolderName,
		arg.CardPan,
		arg.TargetDomain,
		arg.Status,
		arg.OrderType,
		arg.OrderAmount,
		arg.OrderAsset,
		arg.OrderDescription,
		arg.Reference_2,
		arg.VoucherCode,
		arg.MetaData,
	)
	var i Order
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.UserIdentifier,
		&i.CardIdentifier,
		&i.CardHolderName,
		&i.CardPan,
		&i.TargetDomain,
		&i.Status,
		&i.OrderType,
		&i.OrderAmount,
		&i.OrderAsset,
		&i.OrderDescription,
		&i.Reference,
		&i.VoucherCode,
		&i.VoucherDiscountAmount,
		&i.MetaData,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}
