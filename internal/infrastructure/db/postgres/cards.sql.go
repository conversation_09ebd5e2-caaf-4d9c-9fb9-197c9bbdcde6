// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: cards.sql

package postgres

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const createCard = `-- name: CreateCard :one
INSERT INTO cards (
    identifier,
    user_identifier,
    status,
    iin,
    iin_code,
    card_type,
    card_type_code,
    account_number,
    luhn_digit,
    pin_code,
    primary_account_number,
    expire_year,
    expire_month,
    meta_data,
    expires_at,
    cvv1,
    cvv2,
    icvv,
    track_1,
    track_2,
    application_protocol_data_units,
    emv_xml,
    updated_at
  )
VALUES (
    $1,
    $2,
    $3,
    $4,
    $5,
    $6,
    $7,
    $8,
    $9,
    $10,
    $11,
    $12,
    $13,
    $14,
    $15,
    $16,
    $17,
    $18,
    $19,
    $20,
    $21,
    $22,
    CURRENT_TIMESTAMP
  )
RETURNING id, identifier, user_identifier, status, iin, card_type, is_user_default_card, iin_code, card_type_code, account_number, luhn_digit, cvv1, icvv, cvv2, track_1, track_2, application_protocol_data_units, emv_xml, pin_code, primary_account_number, expire_year, expire_month, meta_data, expires_at, created_at, updated_at, deleted_at
`

type CreateCardParams struct {
	Identifier                   string             `json:"identifier"`
	UserIdentifier               string             `json:"user_identifier"`
	Status                       CardStatus         `json:"status"`
	Iin                          int64              `json:"iin"`
	IinCode                      string             `json:"iin_code"`
	CardType                     int64              `json:"card_type"`
	CardTypeCode                 string             `json:"card_type_code"`
	AccountNumber                string             `json:"account_number"`
	LuhnDigit                    string             `json:"luhn_digit"`
	PinCode                      string             `json:"pin_code"`
	PrimaryAccountNumber         string             `json:"primary_account_number"`
	ExpireYear                   string             `json:"expire_year"`
	ExpireMonth                  string             `json:"expire_month"`
	MetaData                     []byte             `json:"meta_data"`
	ExpiresAt                    pgtype.Timestamptz `json:"expires_at"`
	Cvv1                         string             `json:"cvv1"`
	Cvv2                         string             `json:"cvv2"`
	Icvv                         string             `json:"icvv"`
	Track1                       string             `json:"track_1"`
	Track2                       string             `json:"track_2"`
	ApplicationProtocolDataUnits []string           `json:"application_protocol_data_units"`
	EmvXml                       string             `json:"emv_xml"`
}

func (q *Queries) CreateCard(ctx context.Context, arg CreateCardParams) (Card, error) {
	row := q.db.QueryRow(ctx, createCard,
		arg.Identifier,
		arg.UserIdentifier,
		arg.Status,
		arg.Iin,
		arg.IinCode,
		arg.CardType,
		arg.CardTypeCode,
		arg.AccountNumber,
		arg.LuhnDigit,
		arg.PinCode,
		arg.PrimaryAccountNumber,
		arg.ExpireYear,
		arg.ExpireMonth,
		arg.MetaData,
		arg.ExpiresAt,
		arg.Cvv1,
		arg.Cvv2,
		arg.Icvv,
		arg.Track1,
		arg.Track2,
		arg.ApplicationProtocolDataUnits,
		arg.EmvXml,
	)
	var i Card
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.UserIdentifier,
		&i.Status,
		&i.Iin,
		&i.CardType,
		&i.IsUserDefaultCard,
		&i.IinCode,
		&i.CardTypeCode,
		&i.AccountNumber,
		&i.LuhnDigit,
		&i.Cvv1,
		&i.Icvv,
		&i.Cvv2,
		&i.Track1,
		&i.Track2,
		&i.ApplicationProtocolDataUnits,
		&i.EmvXml,
		&i.PinCode,
		&i.PrimaryAccountNumber,
		&i.ExpireYear,
		&i.ExpireMonth,
		&i.MetaData,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getCardByIdentifier = `-- name: GetCardByIdentifier :one
SELECT id, identifier, user_identifier, status, iin, card_type, is_user_default_card, iin_code, card_type_code, account_number, luhn_digit, cvv1, icvv, cvv2, track_1, track_2, application_protocol_data_units, emv_xml, pin_code, primary_account_number, expire_year, expire_month, meta_data, expires_at, created_at, updated_at, deleted_at
FROM cards
WHERE identifier = $1
  AND deleted_at IS NULL
LIMIT 1
`

func (q *Queries) GetCardByIdentifier(ctx context.Context, identifier string) (Card, error) {
	row := q.db.QueryRow(ctx, getCardByIdentifier, identifier)
	var i Card
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.UserIdentifier,
		&i.Status,
		&i.Iin,
		&i.CardType,
		&i.IsUserDefaultCard,
		&i.IinCode,
		&i.CardTypeCode,
		&i.AccountNumber,
		&i.LuhnDigit,
		&i.Cvv1,
		&i.Icvv,
		&i.Cvv2,
		&i.Track1,
		&i.Track2,
		&i.ApplicationProtocolDataUnits,
		&i.EmvXml,
		&i.PinCode,
		&i.PrimaryAccountNumber,
		&i.ExpireYear,
		&i.ExpireMonth,
		&i.MetaData,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getCardByPAN = `-- name: GetCardByPAN :one
SELECT id, identifier, user_identifier, status, iin, card_type, is_user_default_card, iin_code, card_type_code, account_number, luhn_digit, cvv1, icvv, cvv2, track_1, track_2, application_protocol_data_units, emv_xml, pin_code, primary_account_number, expire_year, expire_month, meta_data, expires_at, created_at, updated_at, deleted_at
FROM cards
WHERE primary_account_number = $1
  AND deleted_at IS NULL
LIMIT 1
`

func (q *Queries) GetCardByPAN(ctx context.Context, primaryAccountNumber string) (Card, error) {
	row := q.db.QueryRow(ctx, getCardByPAN, primaryAccountNumber)
	var i Card
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.UserIdentifier,
		&i.Status,
		&i.Iin,
		&i.CardType,
		&i.IsUserDefaultCard,
		&i.IinCode,
		&i.CardTypeCode,
		&i.AccountNumber,
		&i.LuhnDigit,
		&i.Cvv1,
		&i.Icvv,
		&i.Cvv2,
		&i.Track1,
		&i.Track2,
		&i.ApplicationProtocolDataUnits,
		&i.EmvXml,
		&i.PinCode,
		&i.PrimaryAccountNumber,
		&i.ExpireYear,
		&i.ExpireMonth,
		&i.MetaData,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getLastAccountNumberForIinAndCardType = `-- name: GetLastAccountNumberForIinAndCardType :one
SELECT id, identifier, user_identifier, status, iin, card_type, is_user_default_card, iin_code, card_type_code, account_number, luhn_digit, cvv1, icvv, cvv2, track_1, track_2, application_protocol_data_units, emv_xml, pin_code, primary_account_number, expire_year, expire_month, meta_data, expires_at, created_at, updated_at, deleted_at
FROM cards
WHERE iin = $1
  AND card_type = $2
  AND deleted_at IS NULL
ORDER BY id DESC
LIMIT 1
`

type GetLastAccountNumberForIinAndCardTypeParams struct {
	Iin      int64 `json:"iin"`
	CardType int64 `json:"card_type"`
}

func (q *Queries) GetLastAccountNumberForIinAndCardType(ctx context.Context, arg GetLastAccountNumberForIinAndCardTypeParams) (Card, error) {
	row := q.db.QueryRow(ctx, getLastAccountNumberForIinAndCardType, arg.Iin, arg.CardType)
	var i Card
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.UserIdentifier,
		&i.Status,
		&i.Iin,
		&i.CardType,
		&i.IsUserDefaultCard,
		&i.IinCode,
		&i.CardTypeCode,
		&i.AccountNumber,
		&i.LuhnDigit,
		&i.Cvv1,
		&i.Icvv,
		&i.Cvv2,
		&i.Track1,
		&i.Track2,
		&i.ApplicationProtocolDataUnits,
		&i.EmvXml,
		&i.PinCode,
		&i.PrimaryAccountNumber,
		&i.ExpireYear,
		&i.ExpireMonth,
		&i.MetaData,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getUserCards = `-- name: GetUserCards :many
SELECT id, identifier, user_identifier, status, iin, card_type, is_user_default_card, iin_code, card_type_code, account_number, luhn_digit, cvv1, icvv, cvv2, track_1, track_2, application_protocol_data_units, emv_xml, pin_code, primary_account_number, expire_year, expire_month, meta_data, expires_at, created_at, updated_at, deleted_at
FROM cards
WHERE user_identifier = $1
  AND deleted_at IS NULL
ORDER BY created_at DESC
`

func (q *Queries) GetUserCards(ctx context.Context, userIdentifier string) ([]Card, error) {
	rows, err := q.db.Query(ctx, getUserCards, userIdentifier)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Card{}
	for rows.Next() {
		var i Card
		if err := rows.Scan(
			&i.ID,
			&i.Identifier,
			&i.UserIdentifier,
			&i.Status,
			&i.Iin,
			&i.CardType,
			&i.IsUserDefaultCard,
			&i.IinCode,
			&i.CardTypeCode,
			&i.AccountNumber,
			&i.LuhnDigit,
			&i.Cvv1,
			&i.Icvv,
			&i.Cvv2,
			&i.Track1,
			&i.Track2,
			&i.ApplicationProtocolDataUnits,
			&i.EmvXml,
			&i.PinCode,
			&i.PrimaryAccountNumber,
			&i.ExpireYear,
			&i.ExpireMonth,
			&i.MetaData,
			&i.ExpiresAt,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserDefaultCard = `-- name: GetUserDefaultCard :one
SELECT id, identifier, user_identifier, status, iin, card_type, is_user_default_card, iin_code, card_type_code, account_number, luhn_digit, cvv1, icvv, cvv2, track_1, track_2, application_protocol_data_units, emv_xml, pin_code, primary_account_number, expire_year, expire_month, meta_data, expires_at, created_at, updated_at, deleted_at
FROM cards
WHERE user_identifier = $1
  AND is_user_default_card = true
  AND deleted_at IS NULL
LIMIT 1
`

func (q *Queries) GetUserDefaultCard(ctx context.Context, userIdentifier string) (Card, error) {
	row := q.db.QueryRow(ctx, getUserDefaultCard, userIdentifier)
	var i Card
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.UserIdentifier,
		&i.Status,
		&i.Iin,
		&i.CardType,
		&i.IsUserDefaultCard,
		&i.IinCode,
		&i.CardTypeCode,
		&i.AccountNumber,
		&i.LuhnDigit,
		&i.Cvv1,
		&i.Icvv,
		&i.Cvv2,
		&i.Track1,
		&i.Track2,
		&i.ApplicationProtocolDataUnits,
		&i.EmvXml,
		&i.PinCode,
		&i.PrimaryAccountNumber,
		&i.ExpireYear,
		&i.ExpireMonth,
		&i.MetaData,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const updateCard = `-- name: UpdateCard :one
UPDATE cards
SET
  status = $2,
  pin_code = $3,
  meta_data = $4,
  updated_at = CURRENT_TIMESTAMP
WHERE identifier = $1
  AND deleted_at IS NULL
RETURNING id, identifier, user_identifier, status, iin, card_type, is_user_default_card, iin_code, card_type_code, account_number, luhn_digit, cvv1, icvv, cvv2, track_1, track_2, application_protocol_data_units, emv_xml, pin_code, primary_account_number, expire_year, expire_month, meta_data, expires_at, created_at, updated_at, deleted_at
`

type UpdateCardParams struct {
	Identifier string     `json:"identifier"`
	Status     CardStatus `json:"status"`
	PinCode    string     `json:"pin_code"`
	MetaData   []byte     `json:"meta_data"`
}

func (q *Queries) UpdateCard(ctx context.Context, arg UpdateCardParams) (Card, error) {
	row := q.db.QueryRow(ctx, updateCard,
		arg.Identifier,
		arg.Status,
		arg.PinCode,
		arg.MetaData,
	)
	var i Card
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.UserIdentifier,
		&i.Status,
		&i.Iin,
		&i.CardType,
		&i.IsUserDefaultCard,
		&i.IinCode,
		&i.CardTypeCode,
		&i.AccountNumber,
		&i.LuhnDigit,
		&i.Cvv1,
		&i.Icvv,
		&i.Cvv2,
		&i.Track1,
		&i.Track2,
		&i.ApplicationProtocolDataUnits,
		&i.EmvXml,
		&i.PinCode,
		&i.PrimaryAccountNumber,
		&i.ExpireYear,
		&i.ExpireMonth,
		&i.MetaData,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const updateCardPIN = `-- name: UpdateCardPIN :one
UPDATE cards
SET
  pin_code = $2,
  updated_at = CURRENT_TIMESTAMP
WHERE identifier = $1
  AND deleted_at IS NULL
RETURNING id, identifier, user_identifier, status, iin, card_type, is_user_default_card, iin_code, card_type_code, account_number, luhn_digit, cvv1, icvv, cvv2, track_1, track_2, application_protocol_data_units, emv_xml, pin_code, primary_account_number, expire_year, expire_month, meta_data, expires_at, created_at, updated_at, deleted_at
`

type UpdateCardPINParams struct {
	Identifier string `json:"identifier"`
	PinCode    string `json:"pin_code"`
}

func (q *Queries) UpdateCardPIN(ctx context.Context, arg UpdateCardPINParams) (Card, error) {
	row := q.db.QueryRow(ctx, updateCardPIN, arg.Identifier, arg.PinCode)
	var i Card
	err := row.Scan(
		&i.ID,
		&i.Identifier,
		&i.UserIdentifier,
		&i.Status,
		&i.Iin,
		&i.CardType,
		&i.IsUserDefaultCard,
		&i.IinCode,
		&i.CardTypeCode,
		&i.AccountNumber,
		&i.LuhnDigit,
		&i.Cvv1,
		&i.Icvv,
		&i.Cvv2,
		&i.Track1,
		&i.Track2,
		&i.ApplicationProtocolDataUnits,
		&i.EmvXml,
		&i.PinCode,
		&i.PrimaryAccountNumber,
		&i.ExpireYear,
		&i.ExpireMonth,
		&i.MetaData,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}
