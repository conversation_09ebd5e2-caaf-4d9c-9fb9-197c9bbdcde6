// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package postgres

import (
	"context"
)

type Querier interface {
	CountUserUsedVoucher(ctx context.Context, arg CountUserUsedVoucherParams) (int64, error)
	GetOrderByReference(ctx context.Context, reference string) (Order, error)
	GetUserOrderByReference(ctx context.Context, arg GetUserOrderByReferenceParams) (Order, error)
	GetVoucherByCode(ctx context.Context, code string) (Voucher, error)
	ListUserOrders(ctx context.Context, arg ListUserOrdersParams) ([]Order, error)
	SetOrderStatus(ctx context.Context, arg SetOrderStatusParams) error
	SoftDeleteOrder(ctx context.Context, reference string) error
	// insert new order to database or update existing order params where reference is same
	UpsertNewOrder(ctx context.Context, arg UpsertNewOrderParams) (Order, error)
}

var _ Querier = (*Queries)(nil)
