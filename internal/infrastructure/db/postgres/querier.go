// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package postgres

import (
	"context"
)

type Querier interface {
	CreateCard(ctx context.Context, arg CreateCardParams) (Card, error)
	CreateCardType(ctx context.Context, arg CreateCardTypeParams) (CardType, error)
	CreateIssuerIdentifier(ctx context.Context, arg CreateIssuerIdentifierParams) (IssuerIdentifier, error)
	GetCardByIdentifier(ctx context.Context, identifier string) (Card, error)
	GetCardByPAN(ctx context.Context, primaryAccountNumber string) (Card, error)
	GetCardTypeByCode(ctx context.Context, code string) (CardType, error)
	GetCardTypeByIdentifier(ctx context.Context, identifier string) (CardType, error)
	GetDefaultCardType(ctx context.Context) (CardType, error)
	GetDefaultIssuerIdentifier(ctx context.Context) (IssuerIdentifier, error)
	GetIssuerIdentifierByID(ctx context.Context, arg GetIssuerIdentifierByIDParams) (IssuerIdentifier, error)
	GetIssuerIdentifierByIdentifier(ctx context.Context, arg GetIssuerIdentifierByIdentifierParams) (IssuerIdentifier, error)
	GetIssuerIdentifierByNumber(ctx context.Context, iin string) (IssuerIdentifier, error)
	GetIssuerIdentifierByNumberAndStatus(ctx context.Context, arg GetIssuerIdentifierByNumberAndStatusParams) (IssuerIdentifier, error)
	GetIssuerIdentifierByUserIdentifier(ctx context.Context, issuerUserIdentifier string) (IssuerIdentifier, error)
	GetLastAccountNumberForIinAndCardType(ctx context.Context, arg GetLastAccountNumberForIinAndCardTypeParams) (Card, error)
	GetUserCards(ctx context.Context, userIdentifier string) ([]Card, error)
	GetUserDefaultCard(ctx context.Context, userIdentifier string) (Card, error)
	UpdateCard(ctx context.Context, arg UpdateCardParams) (Card, error)
	UpdateCardPIN(ctx context.Context, arg UpdateCardPINParams) (Card, error)
	UpdateCardType(ctx context.Context, arg UpdateCardTypeParams) (CardType, error)
	UpdateIssuerIdentifier(ctx context.Context, arg UpdateIssuerIdentifierParams) (IssuerIdentifier, error)
}

var _ Querier = (*Queries)(nil)
