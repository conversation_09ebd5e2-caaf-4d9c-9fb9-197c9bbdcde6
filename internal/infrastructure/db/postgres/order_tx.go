package postgres

import (
	"context"
)

// UpsertOrderTxParams is the params for UpsertOrderTx.
type UpsertOrderTxParams struct {
	UpsertNewOrderParams
	AfterUpsert func(order Order) error
}

// OrderTxResult is the result for UpsertOrderTx.
type OrderTxResult struct {
	Order Order
}

// UpsertOrderTx creates a new order or update existing and then calls the AfterUpsert function.
func (store *SQLStore) UpsertOrderTx(ctx context.Context, arg UpsertOrderTxParams) (OrderTxResult, error) {
	var result OrderTxResult

	err := store.execTx(ctx, func(q *Queries) error {
		var err error
		result.Order, err = q.UpsertNewOrder(ctx, arg.UpsertNewOrderParams)
		if err != nil {
			return err
		}

		return arg.AfterUpsert(result.Order)
	})

	return result, err
}

// UpdateOrderStatusTxParams is the params for UpdateOrderStatusTx.
type UpdateOrderStatusTxParams struct {
	Reference   string
	Status      OrderStatus
	AfterUpdate func(order Order) error
}

// UpdateOrderStatusTx updates the order status and then calls the AfterUpdate function.
func (store *SQLStore) UpdateOrderStatusTx(ctx context.Context, arg UpdateOrderStatusTxParams) (OrderTxResult, error) {
	var result OrderTxResult

	err := store.execTx(ctx, func(q *Queries) error {
		err := q.SetOrderStatus(ctx, SetOrderStatusParams{
			Reference: arg.Reference,
			Status:    arg.Status,
		})
		if err != nil {
			return err
		}

		return arg.AfterUpdate(result.Order)
	})

	return result, err
}
