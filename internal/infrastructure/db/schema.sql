-- SQL dump generated using DBML (dbml.dbdiagram.io)
-- Database: PostgreSQL
-- Generated at: 2025-07-19T11:37:59.010Z

CREATE TYPE "card_status" AS ENUM (
  'ISSUING',
  'ISSUED',
  'DELIVERED',
  'LEGAL_LOCKED',
  'PIN_LOCKED',
  'USER_LOCKED',
  'EXPIRED_LOCKED',
  'ACTIVE',
  'SUSPENDED',
  'LOST',
  'STOLEN',
  'REPLACEMENT',
  'REPLACED_LOCKED',
  'REPLACED_EXPIRED',
  'REPLACED_BLOCKED',
  'REPLACED_DELETED',
  'REPLACED',
  'EXPIRED',
  'BLOCKED',
  'DELETED'
);

CREATE TYPE "iin_status" AS ENUM (
  'PENDING',
  'REJECTED',
  'ACTIVE',
  'INACTIVE',
  'DELETED',
  'BLOCKED',
  'SUSPENDED',
  'LEG<PERSON>_LOCKED'
);

CREATE TABLE "cards" (
  "id" bigserial PRIMARY KEY,
  "identifier" varchar(64) UNIQUE NOT NULL,
  "user_identifier" varchar(64) UNIQUE NOT NULL,
  "status" card_status NOT NULL DEFAULT 'ISSUING',
  "iin" bigint NOT NULL,
  "card_type" bigint NOT NULL,
  "is_user_default_card" bool NOT NULL DEFAULT false,
  "iin_code" varchar(4) NOT NULL,
  "card_type_code" varchar(1) NOT NULL,
  "account_number" varchar(11) NOT NULL,
  "luhn_digit" varchar(1) NOT NULL,
  "cvv1" varchar(256) NOT NULL,
  "icvv" varchar(256) NOT NULL,
  "cvv2" varchar(256) NOT NULL,
  "track_1" text NOT NULL,
  "track_2" text NOT NULL,
  "application_protocol_data_units" text[] NOT NULL,
  "emv_xml" text NOT NULL,
  "pin_code" varchar(256) NOT NULL,
  "primary_account_number" varchar(16) NOT NULL,
  "expire_year" varchar(2) NOT NULL,
  "expire_month" varchar(2) NOT NULL,
  "meta_data" jsonb NOT NULL DEFAULT ' {}',
  "expires_at" timestamptz,
  "created_at" timestamptz NOT NULL DEFAULT (CURRENT_TIMESTAMP),
  "updated_at" timestamptz,
  "deleted_at" timestamptz
);

CREATE TABLE "issuer_identifiers" (
  "id" bigserial PRIMARY KEY,
  "identifier" varchar(64) UNIQUE NOT NULL,
  "iin" varchar(4) UNIQUE NOT NULL,
  "status" iin_status NOT NULL DEFAULT 'ACTIVE',
  "issuer_user_identifier" varchar(64) UNIQUE NOT NULL,
  "issuer_name" varchar(256) NOT NULL,
  "issuer_logo" varchar(256),
  "issuer_url" varchar(256),
  "des_key" bytea NOT NULL,
  "is_default" boolean NOT NULL DEFAULT false,
  "meta_data" jsonb NOT NULL DEFAULT ' {}',
  "expires_at" timestamptz,
  "created_at" timestamptz NOT NULL DEFAULT (CURRENT_TIMESTAMP),
  "updated_at" timestamptz,
  "deleted_at" timestamptz
);

CREATE TABLE "card_types" (
  "id" bigserial PRIMARY KEY,
  "identifier" varchar(64) UNIQUE NOT NULL,
  "code" varchar(1) UNIQUE NOT NULL,
  "name" varchar(256) NOT NULL,
  "description" varchar(256),
  "meta_data" jsonb NOT NULL DEFAULT ' {}',
  "is_default" boolean NOT NULL DEFAULT false,
  "iin" bigint NOT NULL,
  "created_at" timestamptz NOT NULL DEFAULT (CURRENT_TIMESTAMP),
  "updated_at" timestamptz,
  "deleted_at" timestamptz
);

CREATE INDEX ON "cards" ("id");

CREATE INDEX ON "cards" ("identifier");

CREATE INDEX ON "cards" ("user_identifier");

CREATE INDEX ON "cards" ("status");

CREATE INDEX ON "cards" ("iin");

CREATE INDEX ON "cards" ("account_number");

CREATE INDEX ON "cards" ("primary_account_number");

CREATE INDEX ON "cards" ("expire_year");

CREATE INDEX ON "cards" ("expire_month");

CREATE INDEX ON "cards" ("created_at");

CREATE INDEX ON "cards" ("expires_at");

CREATE INDEX ON "cards" ("updated_at");

CREATE INDEX ON "cards" ("deleted_at");

CREATE INDEX ON "cards" ("id", "identifier", "user_identifier");

CREATE INDEX ON "cards" ("id", "identifier", "status");

CREATE INDEX ON "cards" ("id", "identifier", "account_number", "status");

CREATE INDEX ON "cards" ("id", "user_identifier", "status", "primary_account_number", "deleted_at", "expire_year", "expire_month", "expires_at");

CREATE INDEX ON "cards" ("id", "iin", "account_number", "primary_account_number", "status", "expire_year", "expire_month", "expires_at");

CREATE INDEX ON "issuer_identifiers" ("id");

CREATE INDEX ON "issuer_identifiers" ("iin");

CREATE INDEX ON "issuer_identifiers" ("issuer_name");

CREATE INDEX ON "issuer_identifiers" ("created_at");

CREATE INDEX ON "issuer_identifiers" ("updated_at");

CREATE INDEX ON "issuer_identifiers" ("deleted_at");

CREATE INDEX ON "issuer_identifiers" ("id", "identifier", "iin", "issuer_name", "created_at", "updated_at", "deleted_at");

CREATE INDEX ON "card_types" ("id");

CREATE INDEX ON "card_types" ("identifier");

CREATE INDEX ON "card_types" ("name");

CREATE INDEX ON "card_types" ("code");

CREATE INDEX ON "card_types" ("created_at");

CREATE INDEX ON "card_types" ("updated_at");

CREATE INDEX ON "card_types" ("deleted_at");

CREATE INDEX ON "card_types" ("id", "identifier", "code", "name", "created_at", "updated_at", "deleted_at");

COMMENT ON COLUMN "cards"."id" IS 'card internal system unique id';

COMMENT ON COLUMN "cards"."identifier" IS 'unique external identifier for internal-external system identifier isolation';

COMMENT ON COLUMN "cards"."user_identifier" IS 'owner of card external identifier';

COMMENT ON COLUMN "cards"."status" IS 'card status determinable value';

COMMENT ON COLUMN "cards"."iin" IS 'card issuer identifier id';

COMMENT ON COLUMN "cards"."card_type" IS 'card type id';

COMMENT ON COLUMN "cards"."is_user_default_card" IS 'is this card default card for user or not';

COMMENT ON COLUMN "cards"."iin_code" IS 'card issuer identifier code number';

COMMENT ON COLUMN "cards"."card_type_code" IS 'card / service or issuer defined card type code';

COMMENT ON COLUMN "cards"."account_number" IS 'card owner account number';

COMMENT ON COLUMN "cards"."luhn_digit" IS 'Luhn Checksum digit for given (iin + account_number) string';

COMMENT ON COLUMN "cards"."cvv1" IS 'CVV1 for magnetic stripe (service code 101)';

COMMENT ON COLUMN "cards"."icvv" IS 'iCVV for chip cards (service code 201)';

COMMENT ON COLUMN "cards"."cvv2" IS 'CVV2 for card-not-present transactions (service code 999)';

COMMENT ON COLUMN "cards"."track_1" IS 'track 1 data for magnetic stripe cards';

COMMENT ON COLUMN "cards"."track_2" IS 'track 2 data for magnetic stripe cards';

COMMENT ON COLUMN "cards"."application_protocol_data_units" IS 'application protocol data units for chip cards';

COMMENT ON COLUMN "cards"."emv_xml" IS 'EMV profile in XML format';

COMMENT ON COLUMN "cards"."pin_code" IS 'bcrypted random unique 4 or 6 digit pin code number that used for signing transactions';

COMMENT ON COLUMN "cards"."primary_account_number" IS 'complete card number string using [iin(4) + account_number(11) + luhn_checksum(1) = 16 character';

COMMENT ON COLUMN "cards"."expire_year" IS 'issued card valid before this year';

COMMENT ON COLUMN "cards"."expire_month" IS 'issued card valid before this year / month';

COMMENT ON COLUMN "cards"."meta_data" IS 'card metadatas';

COMMENT ON COLUMN "cards"."expires_at" IS 'when the card will expires';

COMMENT ON COLUMN "cards"."created_at" IS 'when card was created';

COMMENT ON COLUMN "cards"."updated_at" IS 'when card was updated';

COMMENT ON COLUMN "cards"."deleted_at" IS 'when card was deleted';

COMMENT ON COLUMN "issuer_identifiers"."id" IS 'iin registry internal system unique id';

COMMENT ON COLUMN "issuer_identifiers"."identifier" IS 'unique external identifier for internal-external system identifier isolation';

COMMENT ON COLUMN "issuer_identifiers"."iin" IS 'card issuer identifier number';

COMMENT ON COLUMN "issuer_identifiers"."status" IS 'iin registry status determinable value';

COMMENT ON COLUMN "issuer_identifiers"."issuer_user_identifier" IS 'issuer owner user external identifier';

COMMENT ON COLUMN "issuer_identifiers"."issuer_name" IS 'issuer name';

COMMENT ON COLUMN "issuer_identifiers"."issuer_logo" IS 'issuer logo url';

COMMENT ON COLUMN "issuer_identifiers"."issuer_url" IS 'issuer website url';

COMMENT ON COLUMN "issuer_identifiers"."des_key" IS '3DES key for encrypting card data';

COMMENT ON COLUMN "issuer_identifiers"."is_default" IS 'is this iin default issuer for creating signed up users default card or not';

COMMENT ON COLUMN "issuer_identifiers"."meta_data" IS 'iin registry metadatas';

COMMENT ON COLUMN "issuer_identifiers"."expires_at" IS 'when the iin registry will expires';

COMMENT ON COLUMN "issuer_identifiers"."created_at" IS 'when iin registry was created';

COMMENT ON COLUMN "issuer_identifiers"."updated_at" IS 'when iin registry was updated';

COMMENT ON COLUMN "issuer_identifiers"."deleted_at" IS 'when iin registry was deleted';

COMMENT ON COLUMN "card_types"."id" IS 'card type internal system unique id';

COMMENT ON COLUMN "card_types"."identifier" IS 'unique external identifier for internal-external system identifier isolation';

COMMENT ON COLUMN "card_types"."code" IS 'card type code';

COMMENT ON COLUMN "card_types"."name" IS 'card type name';

COMMENT ON COLUMN "card_types"."description" IS 'card type description';

COMMENT ON COLUMN "card_types"."meta_data" IS 'card type metadatas';

COMMENT ON COLUMN "card_types"."is_default" IS 'is this card type default type for creating signed up users default card or not';

COMMENT ON COLUMN "card_types"."iin" IS 'card issuer identifier id';

COMMENT ON COLUMN "card_types"."created_at" IS 'when card type was created';

COMMENT ON COLUMN "card_types"."updated_at" IS 'when card type was updated';

COMMENT ON COLUMN "card_types"."deleted_at" IS 'when card type was deleted';

ALTER TABLE "cards" ADD FOREIGN KEY ("iin") REFERENCES "issuer_identifiers" ("id");

ALTER TABLE "cards" ADD FOREIGN KEY ("card_type") REFERENCES "card_types" ("id");

ALTER TABLE "card_types" ADD FOREIGN KEY ("iin") REFERENCES "issuer_identifiers" ("id");
