Project order_service {
  database_type: 'PostgreSQL'
  Note: '''
  # Order Service Database
  '''
}


// order_status enum
// order_status is used to determine the current status of an order in the system.
// It includes various states such as booked, payment pending, payment settled, payment success, payment failed, cancelled, and expired.
Enum order_status {
  BOOKED [note: 'order booked and waiting for payment']
  PAYMENT_PENDING [note: 'payment pending for order']
  PAYMENT_SETTLED [note: 'payment settled for order']
  PAYMENT_SUCCESS [note: 'payment success for order']
  PAYMENT_FAILED [note: 'payment failed for order']
  USER_CANCELLED [note: 'order cancelled by user']
  SYSTEM_CANCELLED [note: 'order cancelled by system']
  EXPIRED [note: 'order expired']
}


// orders table
// orders table stores all the necessary information about each order, including its status, identifiers, and metadata. The table includes fields for order details such as user identifier, card identifier, card holder name, card pan, target domain, order type, order amount, order asset, order description, reference, and voucher code.
// The table also includes timestamps for creation, updates, and deletion, allowing for tracking the lifecycle of each order.
Table orders {
  id bigserial [pk, increment, not null, unique, note: 'order internal system unique id']
  identifier varchar(64) [not null, unique, note: 'unique external identifier for internal-external system identifier isolation']
  user_identifier varchar(64) [not null, note: 'customer user external identifier']
  card_identifier varchar(64) [not null, note: 'card external identifier']
  card_holder_name varchar(256) [not null, note: 'card holder name']
  card_pan varchar(24) [not null, note: 'card pan number']
  target_domain varchar(256) [not null, note: 'target domain for order']
  status order_status [not null, default: 'BOOKED', note: 'order status determinable value']
  order_type varchar(256) [not null, note: 'order type']
  order_amount varchar(32) [not null, note: 'order amount']
  order_asset varchar(32) [not null, note: 'order asset']
  order_description varchar(256) [not null, note: 'order description']
  reference varchar(256) [not null, unique, note: 'order reference to make it idempotent']
  voucher_code varchar(256) [not null, default: '', note: 'voucher code for order']
  voucher_discount_amount varchar(32) [not null, default: '0', note: 'voucher discount amount']

  meta_data jsonb [not null, default: '{}', note: 'order meta data']

  created_at timestamptz [not null, default: `now()`, note: 'when order was created']
  updated_at timestamptz [note: 'when order was updated']
  deleted_at timestamptz [note: 'when order was deleted']
}

// vouchers table
// vouchers table stores all the available vouchers in the system.
// It includes fields for voucher details such as code, discount percentage, asset, and timestamps for creation, updates, and deletion.
Table vouchers {
  id bigserial [pk, increment, not null, unique, note: 'voucher internal system unique id']
  code varchar(256) [not null, unique, note: 'voucher code']
  description varchar(256) [not null, note: 'voucher description']
  discount_percentage varchar(32) [not null, default: '3', note: 'voucher discount percentage']
  asset varchar(32) [not null, default: 'IRR', note: 'voucher asset']
  per_user_max_usage int [not null, default: 0, note: 'per user max usage for voucher']

  expires_at timestamptz [note: 'when voucher was expired']
  created_at timestamptz [not null, default: `now()`, note: 'when voucher was created']
  updated_at timestamptz [note: 'when voucher was updated']
  deleted_at timestamptz [note: 'when voucher was deleted']
}

