Project card_service {
  database_type: 'PostgreSQL'
  Note: '''
  # Card Service Database
  '''
}

// card_status enum
// card_status is used to determine the current status of a card in the system.
// It includes various states such as issuing, issued, delivered, locked, active, suspended, lost, stolen, replaced, expired, blocked, and deleted.
// Each status has a specific meaning and is used to track the lifecycle of a card.
// The statuses are defined as follows:
Enum card_status {
  ISSUING [note: 'determine card issuing is in progress']
  ISSUED [note: 'card issued and pending to deliver to user']
  DELIVERED [note: 'card delivered to owner']
  LEGAL_LOCKED [note: 'card locked because of legal terms and condition violations']
  PIN_LOCKED [note: 'card locked because of too many incorrect pin entry']
  USER_LOCKED [note: 'card locked by user personal request']
  EXPIRED_LOCKED [note: 'card locked because of expired date']
  ACTIVE [note: 'card is active and ready to use']
  SUSPENDED [note: 'card suspended by system for some reason']
  LOST [note: 'card lost by user']
  STOLEN [note: 'card stolen by someone']
  REPLACEMENT [note: 'card replacement in progress']
  REPLACED_LOCKED [note: 'card replaced and locked']
  REPLACED_EXPIRED [note: 'card replaced and expired']
  REPLACED_BLOCKED [note: 'card replaced and blocked']
  REPLACED_DELETED [note: 'card replaced and deleted']
  REPLACED [note: 'card replaced by new one and ready to use']
  EXPIRED [note: 'card validity time expired']
  BLOCKED [note: 'card blocked completely by system']
  DELETED [note: 'card permanently deleted by network administrations']
}

// cards table contains 'System Master Data' for cards
// It stores all the necessary information about each card, including its status, identifiers, and metadata.
// The table is designed to handle various card statuses and includes fields for card details such as IIN, account number, CVV, pin code, card number, expiration date, and metadata.
// The table also includes timestamps for creation, updates, and deletion, allowing for tracking the lifecycle of each card.
// The table is indexed on various fields to optimize query performance and ensure efficient data retrieval.
// The table structure is as follows:
Table cards {
  id bigserial [pk, note: 'card internal system unique id']
  identifier varchar(64) [unique, not null, note: 'unique external identifier for internal-external system identifier isolation']
  user_identifier varchar(64) [unique, not null, note: 'owner of card external identifier']
  status card_status [not null, default: 'ISSUING', note: 'card status determinable value']

  iin bigint [not null, ref: > issuer_identifiers.id, note: 'card issuer identifier id']
  card_type bigint [not null, ref: > card_types.id, note: 'card type id']

  is_user_default_card bool [not null, default: false, note: 'is this card default card for user or not']

  iin_code varchar(4) [not null, note: 'card issuer identifier code number']
  card_type_code varchar(1) [not null, note: 'card / service or issuer defined card type code']
  account_number varchar(11) [not null, note: 'card owner account number']
  luhn_digit varchar(1) [not null, note: 'Luhn Checksum digit for given (iin + account_number) string']

  cvv1 varchar(256) [not null, note: 'CVV1 for magnetic stripe (service code 101)']
  icvv varchar(256) [not null, note: 'iCVV for chip cards (service code 201)']
  cvv2 varchar(256) [not null, note: 'CVV2 for card-not-present transactions (service code 999)']
  track_1 text [not null, note: 'track 1 data for magnetic stripe cards']
  track_2 text [not null, note: 'track 2 data for magnetic stripe cards']
  application_protocol_data_units text[] [not null, note: 'application protocol data units for chip cards']
  emv_xml text [not null, note: 'EMV profile in XML format']

  pin_code varchar(256) [not null, note: 'bcrypted random unique 4 or 6 digit pin code number that used for signing transactions']
  primary_account_number varchar(16) [not null, note: 'complete card number string using [iin(4) + account_number(11) + luhn_checksum(1) = 16 character']

  expire_year varchar(2) [not null, note: 'issued card valid before this year']
  expire_month varchar(2) [not null, note: 'issued card valid before this year / month']

  meta_data jsonb [not null, default: ' {}', note: 'card metadatas']

  expires_at timestamptz [note: 'when the card will expires']
  created_at timestamptz [not null, default: `CURRENT_TIMESTAMP`, note: 'when card was created']
  updated_at timestamptz [note: 'when card was updated']
  deleted_at timestamptz [note: 'when card was deleted']

  Indexes {
    id
    identifier
    user_identifier
    status
    iin
    account_number
    primary_account_number
    expire_year
    expire_month
    created_at
    expires_at
    updated_at
    deleted_at
    (id, identifier, user_identifier)
    (id, identifier, status)
    (id, identifier, account_number, status)
    (id, user_identifier, status, primary_account_number, deleted_at, expire_year, expire_month, expires_at)
    (id, iin, account_number, primary_account_number, status, expire_year, expire_month, expires_at)
  }

}

// iin_status enum
Enum iin_status {
  PENDING [note: 'iin registry is pending and not ready to use']
  REJECTED [note: 'iin registry is rejected and not available for use']
  ACTIVE [note: 'iin registry is active and ready to use']
  INACTIVE [note: 'iin registry is inactive and not ready to use']
  DELETED [note: 'iin registry is deleted and not available for use']
  BLOCKED [note: 'iin registry is blocked and not available for use']
  SUSPENDED [note: 'iin registry is suspended and not available for use']
  LEGAL_LOCKED [note: 'iin registry is locked because of legal terms and condition violations']
}

// issuer_identifiers table contains 'System Master Data' for IIN (Issuer Identification Number) registry
// It stores information about card issuers, including their unique identifiers, names, logos, and URLs.
// The table is designed to manage the metadata associated with each issuer, allowing for easy retrieval and updates.
// The table includes fields for the issuer's IIN, external identifier, name, logo URL, website URL, and additional metadata.
// It also includes timestamps for creation, updates, and deletion, enabling tracking of the lifecycle of each issuer record.
// The table is indexed on various fields to optimize query performance and ensure efficient data retrieval.
// The table structure is as follows:
Table issuer_identifiers {
  id bigserial [pk, note: 'iin registry internal system unique id']
  identifier varchar(64) [unique, not null, note: 'unique external identifier for internal-external system identifier isolation']
  iin varchar(4) [not null, unique, note: 'card issuer identifier number']
  status iin_status [not null, default: 'ACTIVE', note: 'iin registry status determinable value']

  issuer_user_identifier varchar(64) [not null, unique, note: 'issuer owner user external identifier']
  issuer_name varchar(256) [not null, note: 'issuer name']
  issuer_logo varchar(256) [note: 'issuer logo url']
  issuer_url varchar(256) [note: 'issuer website url']
  des_key bytea [not null, note: '3DES key for encrypting card data']

  is_default boolean [not null, default: false, note: 'is this iin default issuer for creating signed up users default card or not']

  meta_data jsonb [not null, default: ' {}', note: 'iin registry metadatas']

  expires_at timestamptz [note: 'when the iin registry will expires']
  created_at timestamptz [not null, default: `CURRENT_TIMESTAMP`, note: 'when iin registry was created']
  updated_at timestamptz [note: 'when iin registry was updated']
  deleted_at timestamptz [note: 'when iin registry was deleted']

  Indexes {
    id
    iin
    issuer_name
    created_at
    updated_at
    deleted_at
    (id, identifier, iin, issuer_name, created_at, updated_at, deleted_at)
  }

}

// card_types table contains 'System Master Data' for card types
// It stores information about different card types, including their unique identifiers, codes, names, descriptions, and metadata.
// The table is designed to manage the metadata associated with each card type, allowing for easy retrieval and updates.
// The table includes fields for the card type's code, name, description, and additional metadata.
// It also includes timestamps for creation, updates, and deletion, enabling tracking of the lifecycle of each card type record.
Table card_types {
  id bigserial [pk, note: 'card type internal system unique id']
  identifier varchar(64) [unique, not null, note: 'unique external identifier for internal-external system identifier isolation']
  code varchar(1) [unique, not null, note: 'card type code']
  name varchar(256) [not null, note: 'card type name']
  description varchar(256) [note: 'card type description']
  meta_data jsonb [not null, default: ' {}', note: 'card type metadatas']

  is_default boolean [not null, default: false, note: 'is this card type default type for creating signed up users default card or not']

  iin bigint [not null, ref: > issuer_identifiers.id, note: 'card issuer identifier id']

  created_at timestamptz [not null, default: `CURRENT_TIMESTAMP`, note: 'when card type was created']
  updated_at timestamptz [note: 'when card type was updated']
  deleted_at timestamptz [note: 'when card type was deleted']

  Indexes {
    id
    identifier
    name
    code
    created_at
    updated_at
    deleted_at
    (id, identifier, code, name, created_at, updated_at, deleted_at)
  }

}