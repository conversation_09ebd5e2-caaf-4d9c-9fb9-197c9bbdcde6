-- name: GetCardTypeByCode :one
SELECT *
FROM card_types
WHERE code = $1
    AND deleted_at IS NULL
LIMIT 1;

-- name: GetDefaultCardType :one
SELECT *
FROM card_types
WHERE is_default = true
    AND deleted_at IS NULL
LIMIT 1;

-- name: GetCardTypeByIdentifier :one
SELECT *
FROM card_types
WHERE identifier = $1
    AND deleted_at IS NULL
LIMIT 1;

-- name: UpdateCardType :one
UPDATE card_types
SET
  name = $2,
  description = $3,
  meta_data = $4,
  updated_at = CURRENT_TIMESTAMP
WHERE code = $1
  AND deleted_at IS NULL
RETURNING *;

-- name: CreateCardType :one
INSERT INTO card_types (
  identifier,
  code,
  name,
  description,
  iin,
  meta_data,
  created_at,
  updated_at
) VALUES (
  $1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
) RETURNING *;
