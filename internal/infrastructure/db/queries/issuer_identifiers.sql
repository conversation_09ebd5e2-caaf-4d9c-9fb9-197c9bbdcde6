-- name: GetDefaultIssuerIdentifier :one
SELECT *
FROM issuer_identifiers
WHERE is_default = true
    AND status = 'ACTIVE'
    AND deleted_at IS NULL
LIMIT 1;


-- name: GetIssuerIdentifierByID :one
SELECT *
FROM issuer_identifiers
WHERE id = $1
    AND status = $2
    AND deleted_at IS NULL
LIMIT 1;

-- name: GetIssuerIdentifierByNumberAndStatus :one
SELECT *
FROM issuer_identifiers
WHERE iin = $1
    AND status = $2
    AND deleted_at IS NULL
LIMIT 1;

-- name: GetIssuerIdentifierByNumber :one
SELECT *
FROM issuer_identifiers
WHERE iin = $1
    AND deleted_at IS NULL
LIMIT 1;

-- name: GetIssuerIdentifierByIdentifier :one
SELECT *
FROM issuer_identifiers
WHERE identifier = $1
    AND status = $2
    AND deleted_at IS NULL
LIMIT 1;

-- name: GetIssuerIdentifierByUserIdentifier :one
SELECT *
FROM issuer_identifiers
WHERE issuer_user_identifier = $1
    AND deleted_at IS NULL
LIMIT 1;

-- name: CreateIssuerIdentifier :one
INSERT INTO issuer_identifiers (
  identifier,
  iin,
  status,
  issuer_user_identifier,
  issuer_name,
  issuer_logo,
  issuer_url,
  meta_data,
  expires_at,
  updated_at,
  des_key
) VALUES (
  $1, $2, $3, $4, $5, $6, $7, $8, $9, CURRENT_TIMESTAMP, $10
)
RETURNING *;

-- name: UpdateIssuerIdentifier :one
UPDATE issuer_identifiers
SET
  status = $2,
  issuer_name = $3,
  issuer_logo = $4,
  issuer_url = $5,
  meta_data = $6,
  expires_at = $7,
  updated_at = CURRENT_TIMESTAMP
WHERE identifier = $1
    AND deleted_at IS NULL
RETURNING *;
