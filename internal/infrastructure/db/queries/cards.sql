-- name: GetUserDefaultCard :one
SELECT *
FROM cards
WHERE user_identifier = $1
  AND is_user_default_card = true
  AND deleted_at IS NULL
LIMIT 1;


-- name: GetUserCards :many
SELECT *
FROM cards
WHERE user_identifier = $1
  AND deleted_at IS NULL
ORDER BY created_at DESC;

-- name: GetLastAccountNumberForIinAndCardType :one
SELECT *
FROM cards
WHERE iin = $1
  AND card_type = $2
  AND deleted_at IS NULL
ORDER BY id DESC
LIMIT 1;

-- name: GetCardByIdentifier :one
SELECT *
FROM cards
WHERE identifier = $1
  AND deleted_at IS NULL
LIMIT 1;

-- name: UpdateCard :one
UPDATE cards
SET
  status = $2,
  pin_code = $3,
  meta_data = $4,
  updated_at = CURRENT_TIMESTAMP
WHERE identifier = $1
  AND deleted_at IS NULL
RETURNING *;

-- name: UpdateCardPIN :one
UPDATE cards
SET
  pin_code = $2,
  updated_at = CURRENT_TIMESTAMP
WHERE identifier = $1
  AND deleted_at IS NULL
RETURNING *;

-- name: GetCardByPAN :one
SELECT *
FROM cards
WHERE primary_account_number = $1
  AND deleted_at IS NULL
LIMIT 1;

-- name: CreateCard :one
INSERT INTO cards (
    identifier,
    user_identifier,
    status,
    iin,
    iin_code,
    card_type,
    card_type_code,
    account_number,
    luhn_digit,
    pin_code,
    primary_account_number,
    expire_year,
    expire_month,
    meta_data,
    expires_at,
    cvv1,
    cvv2,
    icvv,
    track_1,
    track_2,
    application_protocol_data_units,
    emv_xml,
    updated_at
  )
VALUES (
    $1,
    $2,
    $3,
    $4,
    $5,
    $6,
    $7,
    $8,
    $9,
    $10,
    $11,
    $12,
    $13,
    $14,
    $15,
    $16,
    $17,
    $18,
    $19,
    $20,
    $21,
    $22,
    CURRENT_TIMESTAMP
  )
RETURNING *;