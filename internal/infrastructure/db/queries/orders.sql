-- name: Get<PERSON>rderByReference :one
SELECT *
FROM orders
WHERE reference = $1
    AND deleted_at IS NULL
LIMIT 1;

-- name: CountUserUsedVoucher :one
SELECT COUNT(*)
FROM orders
WHERE voucher_code = $1
    AND user_identifier = $2
    AND deleted_at IS NULL;

-- name: GetUserOrderByReference :one
SELECT *
FROM orders
WHERE reference = $1
    AND user_identifier = $2
    AND deleted_at IS NULL
LIMIT 1;

-- name: ListUserOrders :many
SELECT *
FROM orders
WHERE user_identifier = $1
    AND deleted_at IS NULL
ORDER BY created_at DESC
LIMIT $2 OFFSET $3;

-- name: SetOrderStatus :exec
UPDATE orders
SET status = $1,
    updated_at = CURRENT_TIMESTAMP
WHERE reference = $2
    AND deleted_at IS NULL
RETURNING *;

-- name: SoftDeleteOrder :exec
UPDATE orders
SET deleted_at = CURRENT_TIMESTAMP,
    updated_at = CURRENT_TIMESTAMP
WHERE reference = $1
    AND deleted_at IS NULL
RETURNING *;

-- insert new order to database or update existing order params where reference is same
-- name: UpsertNewOrder :one
INSERT INTO orders (
        identifier,
        reference,
        user_identifier,
        card_identifier,
        card_holder_name,
        card_pan,
        target_domain,
        status,
        order_type,
        order_amount,
        order_asset,
        order_description,
        reference,
        voucher_code,
        meta_data,
        created_at,
        updated_at
    )
VALUES (
        $1,
        $2,
        $3,
        $4,
        $5,
        $6,
        $7,
        $8,
        $9,
        $10,
        $11,
        $12,
        $13,
        $14,
        $15,
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP
    ) ON CONFLICT (reference) DO
UPDATE
SET user_identifier = EXCLUDED.user_identifier,
    card_identifier = EXCLUDED.card_identifier,
    card_holder_name = EXCLUDED.card_holder_name,
    card_pan = EXCLUDED.card_pan,
    target_domain = EXCLUDED.target_domain,
    status = EXCLUDED.status,
    order_type = EXCLUDED.order_type,
    order_amount = EXCLUDED.order_amount,
    order_asset = EXCLUDED.order_asset,
    order_description = EXCLUDED.order_description,
    voucher_code = EXCLUDED.voucher_code,
    meta_data = EXCLUDED.meta_data
WHERE orders.deleted_at IS NULL
RETURNING *;