package health

import (
	"context"
	"io"

	"github.com/dapr/go-sdk/client"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/liveutil/order_service/internal/domain"
	"github.com/liveutil/order_service/pb"
	"github.com/nats-io/nats.go"
	"github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/mongo"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type HealthServiceOpts struct {
	Database   *pgxpool.Pool
	Redis      *redis.Client
	Mongo      *mongo.Client
	NATS       *nats.Conn
	DaprClient client.Client
}

type grpcService struct {
	pb.UnimplementedHealthServer
	service domain.HealthService
}

// NewHealthGRPCService creates a new gRPC health service
func NewHealthGRPCService(opts *HealthServiceOpts) pb.HealthServer {
	service := NewHealthService(opts)
	return &grpcService{
		service: service,
	}
}

// Check implements the gRPC health check endpoint
func (s *grpcService) Check(ctx context.Context, req *pb.HealthCheckRequest) (*pb.HealthCheckResponse, error) {
	healthStatus, err := s.service.Check(ctx)
	if err != nil {
		return nil, status.Error(codes.Internal, "health check failed")
	}

	return &pb.HealthCheckResponse{
		Status:    healthStatus.Status,
		Timestamp: healthStatus.Timestamp.Unix(),
		Details: &pb.Details{
			Database:    healthStatus.Details.Database,
			Redis:       healthStatus.Details.Redis,
			Mongodb:     healthStatus.Details.MongoDB,
			MessageBus:  healthStatus.Details.MessageBus,
			ServiceMesh: healthStatus.Details.ServiceMesh,
		},
	}, nil
}

// Watch implements the gRPC health watch endpoint
func (s *grpcService) Watch(req *pb.HealthCheckRequest, stream pb.Health_WatchServer) error {
	statusChan, err := s.service.Watch(stream.Context())
	if err != nil {
		return status.Error(codes.Internal, "failed to start health watch")
	}

	for healthStatus := range statusChan {
		response := &pb.HealthCheckResponse{
			Status:    healthStatus.Status,
			Timestamp: healthStatus.Timestamp.Unix(),
			Details: &pb.Details{
				Database:    healthStatus.Details.Database,
				Redis:       healthStatus.Details.Redis,
				Mongodb:     healthStatus.Details.MongoDB,
				MessageBus:  healthStatus.Details.MessageBus,
				ServiceMesh: healthStatus.Details.ServiceMesh,
			},
		}

		if err := stream.Send(response); err != nil {
			if err == io.EOF {
				return nil
			}
			return status.Error(codes.Internal, "failed to send health status")
		}
	}

	return nil
}
