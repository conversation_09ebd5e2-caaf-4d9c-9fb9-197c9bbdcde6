package card

import (
	formancesdkgo "github.com/formancehq/formance-sdk-go/v3"
	kitlog "github.com/go-kit/log"
	"github.com/liveutil/card_service/internal/config"
	"github.com/liveutil/card_service/internal/infrastructure/db/postgres"
	"github.com/liveutil/card_service/pb"
	"github.com/liveutil/go-lib/paseto"
	"github.com/liveutil/go-lib/servicemesh"
	"github.com/nats-io/nats.go"
	"github.com/redis/go-redis/v9"
	"go.opentelemetry.io/otel/trace"
)

type CardServiceOpts struct {
	Repository       postgres.Store
	Config           *config.Configuration
	Redis            *redis.Client
	NATS             *nats.Conn
	UsersServiceMesh servicemesh.UserMeshService
	Logger           kitlog.Logger
	PASETO           paseto.Maker
	SchemaPath       string
	ApplicationName  string
	Tracer           trace.Tracer
	FormanceClient   *formancesdkgo.Formance
}

// NewCardService creates a new user service with all middleware layers
func NewCardService(opts *CardServiceOpts) (pb.CardServiceServer, error) {
	// Create base service
	svc := NewService(opts)

	// Add middleware layers
	svc = NewAuthorizationMiddleware(svc)

	return svc, nil
}
