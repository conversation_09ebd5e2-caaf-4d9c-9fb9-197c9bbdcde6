package card

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/liveutil/card_service/internal/config"
	"github.com/liveutil/card_service/internal/infrastructure/db/postgres"
	"github.com/liveutil/card_service/pb"
	"github.com/liveutil/go-lib/card_iso"
	"github.com/liveutil/go-lib/servicemesh"
	"github.com/liveutil/go-lib/structure"
	"github.com/nats-io/nats.go"
)

// cardMeshService implements the card mesh service
type cardMeshService struct {
	repo          postgres.Store
	nats          *nats.Conn
	app           string
	subscriptions map[string]*nats.Subscription
	users         servicemesh.UserMeshService
	config        *config.Configuration
	service       pb.CardAdministrationServiceServer
}

func NewMeshService(opts *CardServiceOpts, service pb.CardAdministrationServiceServer) servicemesh.CardMeshService {
	return &cardMeshService{
		repo:          opts.Repository,
		nats:          opts.NATS,
		app:           opts.ApplicationName,
		subscriptions: make(map[string]*nats.Subscription),
		users:         opts.UsersServiceMesh,
		service:       service,
		config:        opts.Config,
	}
}

// GenerateDefaultCardForUser implements servicemesh.CardMeshService.
func (c *cardMeshService) GenerateDefaultCardForUser(ctx context.Context, message servicemesh.CardMeshServiceMessage) (*servicemesh.CardModel, error) {
	userDefaultCard, err := c.repo.GetUserDefaultCard(ctx, message.UserIdentifier)
	if err == nil {
		return &servicemesh.CardModel{
			Identifier:     userDefaultCard.Identifier,
			UserIdentifier: userDefaultCard.UserIdentifier,
			Status:         string(userDefaultCard.Status),
			Iin:            userDefaultCard.Iin,
			CardType:       userDefaultCard.CardType,
			IinCode:        userDefaultCard.IinCode,
			CardTypeCode:   userDefaultCard.CardTypeCode,
			AccountNumber:  userDefaultCard.AccountNumber,
			LuhnDigit:      userDefaultCard.LuhnDigit,
			Cvv:            userDefaultCard.Cvv2,
			PinCode:        userDefaultCard.PinCode,
			CardNumber:     userDefaultCard.PrimaryAccountNumber,
			ExpireYear:     userDefaultCard.ExpireYear,
			ExpireMonth:    userDefaultCard.ExpireMonth,
			MetaData:       userDefaultCard.MetaData,
			CreatedAt:      userDefaultCard.CreatedAt,
			UpdatedAt:      &userDefaultCard.UpdatedAt.Time,
			ExpiresAt:      &userDefaultCard.ExpiresAt.Time,
		}, nil
	}

	if !errors.Is(err, pgx.ErrNoRows) {
		return nil, err
	}

	defaultIIN, err := c.repo.GetDefaultIssuerIdentifier(ctx)
	if err != nil {
		return nil, errors.New("issuer identifier not found")
	}

	defaultCardType, err := c.repo.GetDefaultCardType(ctx)
	if err != nil {
		return nil, errors.New("card type not found")
	}

	user, err := c.users.GetSafeUserByIdentifier(ctx, message.UserIdentifier)
	if err != nil {
		return nil, errors.New("user not found")
	}

	metadata := make(map[string]any)
	metadata["versions"] = map[string]any{
		"version":   1,
		"generator": "card_service_mesh",
		"timestamp": time.Now().UTC().Unix(),
		"data": map[string]any{
			"card_type": defaultCardType.Code,
			"iin":       defaultIIN.Iin,
			"card_holder": map[string]any{
				"identifier": user.Identifier,
				"profile":    user.Profile.Identifier,
				"contact":    user.Profile.Identifier,
			},
		},
	}

	data, err := json.Marshal(metadata)
	if err != nil {
		return nil, err
	}

	// calculate expire date by using default expire years service configuration
	expireDate := time.Now().UTC().Add(time.Hour * 24 * 365 * time.Duration(c.config.DefaultCardExpireYears))
	yy, mm := card_iso.GetCardExpireMonthDayFromDate(expireDate)

	generatedCard, err := c.repo.CreateCardTx(ctx, postgres.CreateCardTxParams{
		GenerateCardParams: postgres.GenerateCardParams{
			UserIdentifier:     user.Identifier,
			IinIdentifier:      defaultIIN.Identifier,
			CardTypeIdentifier: defaultCardType.Identifier,
			ExpiresAt:          expireDate,
			ExpireMonth:        mm,
			ExpireYear:         yy,
			Name:               fmt.Sprintf("%s %s", user.Profile.FirstName, user.Profile.LastName),
			Metadata:           data,
		}, AfterCreate: func(card postgres.Card) error {
			return nil
		},
	})

	if err != nil {
		return nil, err
	}

	return &servicemesh.CardModel{
		Identifier:     generatedCard.Card.Identifier,
		UserIdentifier: generatedCard.Card.UserIdentifier,
		Status:         string(generatedCard.Card.Status),
		Iin:            generatedCard.Card.Iin,
		CardType:       generatedCard.Card.CardType,
		IinCode:        generatedCard.Card.IinCode,
		CardTypeCode:   generatedCard.Card.CardTypeCode,
		AccountNumber:  generatedCard.Card.AccountNumber,
		LuhnDigit:      generatedCard.Card.LuhnDigit,
		Cvv:            generatedCard.Card.Cvv2,
		PinCode:        generatedCard.Card.PinCode,
		CardNumber:     generatedCard.Card.PrimaryAccountNumber,
		ExpireYear:     generatedCard.Card.ExpireYear,
		ExpireMonth:    generatedCard.Card.ExpireMonth,
		MetaData:       []byte(generatedCard.Card.MetaData),
		ExpiresAt:      &generatedCard.Card.ExpiresAt.Time,
		CreatedAt:      generatedCard.Card.CreatedAt,
	}, nil

}

// GetSafeCardByCardNumber implements servicemesh.CardMeshService.
func (c *cardMeshService) GetSafeCardByCardNumber(ctx context.Context, cardNumber string) (*servicemesh.CardModel, error) {
	card, err := c.repo.GetCardByPAN(ctx, cardNumber)
	if err != nil {
		return nil, err
	}

	return &servicemesh.CardModel{
		Identifier:     card.Identifier,
		UserIdentifier: card.UserIdentifier,
		Status:         string(card.Status),
		Iin:            card.Iin,
		CardType:       card.CardType,
		IinCode:        card.IinCode,
		CardTypeCode:   card.CardTypeCode,
		AccountNumber:  card.AccountNumber,
		LuhnDigit:      card.LuhnDigit,
		Cvv:            card.Cvv2,
		PinCode:        card.PinCode,
		CardNumber:     card.PrimaryAccountNumber,
		ExpireYear:     card.ExpireYear,
		ExpireMonth:    card.ExpireMonth,
		MetaData:       card.MetaData,
		ExpiresAt:      &card.ExpiresAt.Time,
		CreatedAt:      card.CreatedAt,
	}, nil
}

// GetSafeCardsByUserIdentifier implements servicemesh.CardMeshService.
func (c *cardMeshService) GetSafeCardsByUserIdentifier(ctx context.Context, userIdentifier string) ([]*servicemesh.CardModel, error) {
	cards, err := c.repo.GetUserCards(ctx, userIdentifier)
	if err != nil {
		return nil, err
	}

	// Convert []postgres.Card to []any for structure.ConvertSlice
	cardInterfaces := make([]any, len(cards))
	for i, card := range cards {
		cardInterfaces[i] = card
	}

	dto, err := structure.ConvertSlice[*servicemesh.CardModel](cardInterfaces)
	if err != nil {
		return nil, err
	}

	return dto, nil
}

// GetSafeCardTypeByTypeCode implements servicemesh.CardMeshService.
func (c *cardMeshService) GetSafeCardTypeByTypeCode(ctx context.Context, typeCode string) (*servicemesh.CardTypeModel, error) {
	card, err := c.repo.GetCardTypeByCode(ctx, typeCode)
	if err != nil {
		return nil, err
	}

	dto, err := structure.ConvertStruct[*servicemesh.CardTypeModel](card)
	if err != nil {
		return nil, err
	}

	return dto, nil
}

// GetSafeIssuerByIIN implements servicemesh.CardMeshService.
func (c *cardMeshService) GetSafeIssuerByIIN(ctx context.Context, iin string) (*servicemesh.IssuerIdentifier, error) {
	card, err := c.repo.GetIssuerIdentifierByNumber(ctx, iin)
	if err != nil {
		return nil, err
	}

	dto, err := structure.ConvertStruct[*servicemesh.IssuerIdentifier](card)
	if err != nil {
		return nil, err
	}

	return dto, nil
}

// GetSafeIssuerByIdentifier implements servicemesh.CardMeshService.
func (c *cardMeshService) GetSafeIssuerByIdentifier(ctx context.Context, identifier string) (*servicemesh.IssuerIdentifier, error) {
	card, err := c.repo.GetIssuerIdentifierByIdentifier(ctx, postgres.GetIssuerIdentifierByIdentifierParams{
		Identifier: identifier,
		Status:     postgres.IinStatusACTIVE,
	})
	if err != nil {
		return nil, err
	}

	dto, err := structure.ConvertStruct[*servicemesh.IssuerIdentifier](card)
	if err != nil {
		return nil, err
	}

	return dto, nil
}

// Start implements servicemesh.CardMeshService.
func (c *cardMeshService) Start(ctx context.Context, subjects []string) error {
	for _, subject := range subjects {
		sub, err := c.nats.QueueSubscribe(subject, c.app, func(msg *nats.Msg) {
			fmt.Println("mesh service message arrived", string(msg.Data))
			message := &servicemesh.CardMeshServiceMessage{}
			if err := json.Unmarshal(msg.Data, message); err != nil {
				msg.Respond([]byte("error: invalid message format"))
				return
			}

			switch msg.Subject {
			case servicemesh.CARD_SERVICE_GET_SAFE_CARD_TYPE_BY_TYPE_CODE:
				if message.CardTypeCode == "" {
					c.nats.Publish(msg.Reply, []byte("error: card type code is required"))
					return
				}
				cardType, err := c.repo.GetCardTypeByCode(ctx, message.CardTypeCode)
				if err != nil {
					c.nats.Publish(msg.Reply, []byte(err.Error()))
					return
				}
				dto, err := structure.ConvertStruct[servicemesh.CardTypeModel](cardType)
				if err != nil {
					c.nats.Publish(msg.Reply, []byte(err.Error()))
					return
				}

				data, err := json.Marshal(dto)
				if err != nil {
					c.nats.Publish(msg.Reply, []byte(err.Error()))
					return
				}
				c.nats.Publish(msg.Reply, data)
			case servicemesh.CARD_SERVICE_GET_SAFE_CARD_BY_CARD_NUMBER:
				if message.CardNumber == "" {
					c.nats.Publish(msg.Reply, []byte("error: card number is required"))
					return
				}
				card, err := c.repo.GetCardByPAN(ctx, message.CardNumber)
				if err != nil {
					c.nats.Publish(msg.Reply, []byte(err.Error()))
					return
				}
				dto, err := structure.ConvertStruct[servicemesh.CardModel](card)
				if err != nil {
					c.nats.Publish(msg.Reply, []byte(err.Error()))
					return
				}

				data, err := json.Marshal(dto)
				if err != nil {
					c.nats.Publish(msg.Reply, []byte(err.Error()))
					return
				}
				c.nats.Publish(msg.Reply, data)
			case servicemesh.CARD_SERVICE_GET_SAFE_ISSUER_IDENTIFIER_BY_IDENTIFIER:
				if message.IssuerIdentifier == "" {
					c.nats.Publish(msg.Reply, []byte("error: iin is required"))
					return
				}
				issuer, err := c.repo.GetIssuerIdentifierByNumber(ctx, message.IssuerIdentifier)
				if err != nil {
					c.nats.Publish(msg.Reply, []byte(err.Error()))
					return
				}
				dto, err := structure.ConvertStruct[servicemesh.IssuerIdentifier](issuer)
				if err != nil {
					c.nats.Publish(msg.Reply, []byte(err.Error()))
					return
				}

				data, err := json.Marshal(dto)
				if err != nil {
					c.nats.Publish(msg.Reply, []byte(err.Error()))
					return
				}
				c.nats.Publish(msg.Reply, data)
			case servicemesh.CARD_SERVICE_GET_SAFE_ISSUER_IDENTIFIER_BY_IIN:
				if message.IssuerIdentifier == "" {
					c.nats.Publish(msg.Reply, []byte("error: iin is required"))
					return
				}
				issuer, err := c.repo.GetIssuerIdentifierByNumber(ctx, message.IssuerIdentifier)
				if err != nil {
					c.nats.Publish(msg.Reply, []byte(err.Error()))
					return
				}
				dto, err := structure.ConvertStruct[servicemesh.IssuerIdentifier](issuer)
				if err != nil {
					c.nats.Publish(msg.Reply, []byte(err.Error()))
					return
				}

				data, err := json.Marshal(dto)
				if err != nil {
					c.nats.Publish(msg.Reply, []byte(err.Error()))
					return
				}
				c.nats.Publish(msg.Reply, data)
			case servicemesh.CARD_SERVICE_GENERATE_DEFAULT_CARD_FOR_USER:
				card, err := c.GenerateDefaultCardForUser(ctx, servicemesh.CardMeshServiceMessage{
					IssuerIdentifier: message.IssuerIdentifier,
					CardTypeCode:     message.CardTypeCode,
					UserIdentifier:   message.UserIdentifier,
				})
				if err != nil {
					c.nats.Publish(msg.Reply, []byte(err.Error()))
					return
				}

				dto, err := structure.ConvertStruct[servicemesh.CardModel](card)
				if err != nil {
					c.nats.Publish(msg.Reply, []byte(err.Error()))
					return
				}

				data, err := json.Marshal(dto)
				if err != nil {
					c.nats.Publish(msg.Reply, []byte(err.Error()))
					return
				}
				c.nats.Publish(msg.Reply, data)
			}
		})
		if err != nil {
			return err
		}
		c.subscriptions[subject] = sub
	}

	return nil
}

// Stop implements servicemesh.CardMeshService.
func (c *cardMeshService) Stop(ctx context.Context) error {
	for subject, sub := range c.subscriptions {
		if err := sub.Unsubscribe(); err != nil {
			return err
		}
		delete(c.subscriptions, subject)
	}
	return nil
}
