package card

import (
	"context"

	"github.com/liveutil/card_service/pb"
)

type authorizationMiddleware struct {
	pb.UnimplementedCardServiceServer
	// next is the next service in the middleware chain
	next pb.CardServiceServer
}

// NewAuthorizationMiddleware returns new authorization layer for pb.CardServiceServer
func NewAuthorizationMiddleware(service pb.CardServiceServer) pb.CardServiceServer {
	return &authorizationMiddleware{
		next: service,
	}
}

// ContextUserCards implements card_service.CardServiceServer.
func (a *authorizationMiddleware) ContextUserCards(ctx context.Context, req *pb.Empty) (*pb.ContextUserCardsResponse, error) {
	return a.next.ContextUserCards(ctx, req)
}

// ValidateCard implements card_service.CardServiceServer.
func (a *authorizationMiddleware) ValidateCard(ctx context.Context, req *pb.ValidateCardRequest) (*pb.ValidateCardResponse, error) {
	return a.next.ValidateCard(ctx, req)
}
