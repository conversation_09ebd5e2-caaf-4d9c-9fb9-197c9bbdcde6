package card

import (
	"context"
	"fmt"

	"github.com/liveutil/card_service/internal/config"
	"github.com/liveutil/card_service/internal/infrastructure/db/postgres"
	"github.com/liveutil/card_service/pb"
	"github.com/liveutil/go-lib/contextutil"
	"github.com/liveutil/go-lib/paseto"
	"github.com/liveutil/go-lib/servicemesh"
	"go.opentelemetry.io/otel/trace"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type service struct {
	pb.UnimplementedCardServiceServer

	repo   postgres.Store
	paseto paseto.Maker
	config *config.Configuration
	users  servicemesh.UserMeshService
	tracer trace.Tracer
}

// CardServiceOpts defines options for creating a new CardService.
func NewService(opts *CardServiceOpts) pb.CardServiceServer {
	return &service{
		repo:   opts.Repository,
		config: opts.Config,
		paseto: opts.PASETO,
		users:  opts.UsersServiceMesh,
		tracer: opts.Tracer,
	}
}

// ContextUserCards implements card_service.CardServiceServer.
func (s *service) ContextUserCards(ctx context.Context, req *pb.Empty) (*pb.ContextUserCardsResponse, error) {
	user := &servicemesh.UserModel{}

	err := contextutil.CatchUser(ctx, user)
	if err != nil {
		return nil, err
	}

	user, err = s.users.GetSafeUserByID(ctx, user.ID)
	if err != nil {
		return nil, err
	}

	cards, err := s.repo.GetUserCards(ctx, user.Identifier)
	if err != nil {
		return nil, err
	}

	// convert []postgres.Card to []*pb.Card
	dto := make([]*pb.CardPAN, len(cards))
	for i, card := range cards {
		dto[i] = &pb.CardPAN{
			Identifier:     card.Identifier,
			UserIdentifier: card.UserIdentifier,
			// @ToDo: fetch profile from users mesh service and fill it
			HolderName:  fmt.Sprintf("%s %s", user.Profile.FirstName, user.Profile.LastName),
			Status:      string(card.Status),
			CardNumber:  card.PrimaryAccountNumber,
			Cvv:         card.Cvv2,
			ExpireYear:  card.ExpireYear,
			ExpireMonth: card.ExpireMonth,
			ExpiresAt:   timestamppb.New(card.ExpiresAt.Time),
			CreatedAt:   timestamppb.New(card.CreatedAt),
			UpdatedAt:   timestamppb.New(card.UpdatedAt.Time),
			DeletedAt:   timestamppb.New(card.DeletedAt.Time),
		}
	}

	return &pb.ContextUserCardsResponse{
		Error:   false,
		Message: "success",
		Cards:   dto,
	}, nil
}

// ValidateCard implements card_service.CardServiceServer.
func (s *service) ValidateCard(ctx context.Context, req *pb.ValidateCardRequest) (*pb.ValidateCardResponse, error) {
	return nil, nil
}
