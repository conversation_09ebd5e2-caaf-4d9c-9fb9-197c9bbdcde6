package admin

import (
	"context"
	"fmt"
	"math/big"
	"strconv"
	"strings"
	"time"

	formancesdkgo "github.com/formancehq/formance-sdk-go/v3"
	"github.com/formancehq/formance-sdk-go/v3/pkg/models/operations"
	"github.com/formancehq/formance-sdk-go/v3/pkg/models/shared"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/liveutil/card_service/internal/config"
	"github.com/liveutil/card_service/internal/domain/transaction"
	"github.com/liveutil/card_service/internal/infrastructure/db/postgres"
	"github.com/liveutil/card_service/pb"
	"github.com/liveutil/go-lib/paseto"
	"github.com/liveutil/go-lib/servicemesh"
	"github.com/liveutil/go-lib/stringutil"
	"github.com/liveutil/go-lib/structure"
	"github.com/oklog/ulid/v2"
	"go.opentelemetry.io/otel/trace"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
)

var DEFAULT_DES_KEY = []byte{0x01, 0x23, 0x45, 0x67, 0x89, 0xAB, 0xCD, 0xEF, 0x01, 0x23, 0x45, 0x67, 0x89, 0xAB, 0xCD, 0xEF}

type administrationService struct {
	pb.UnimplementedCardAdministrationServiceServer

	repo     postgres.Store
	paseto   paseto.Maker
	config   *config.Configuration
	users    servicemesh.UserMeshService
	tracer   trace.Tracer
	formance *formancesdkgo.Formance
}

// NewAdministrationService creates a new card administration service instance
func NewAdministrationService(opts *CardAdministrationServiceOpts) pb.CardAdministrationServiceServer {
	return &administrationService{
		repo:     opts.Repository,
		config:   opts.Config,
		paseto:   opts.PASETO,
		users:    opts.UsersServiceMesh,
		tracer:   opts.Tracer,
		formance: opts.FormanceClient,
	}
}

// CreateCard implements pb.CardAdministrationServiceServer.
func (s *administrationService) CreateCard(ctx context.Context, req *pb.CreateCardRequest) (*pb.CardActionsTypeResponse, error) {
	user, err := s.users.GetSafeUserByIdentifier(ctx, req.UserIdentifier)
	if err != nil {
		return nil, err
	}

	card, err := s.repo.CreateCardTx(ctx, postgres.CreateCardTxParams{
		GenerateCardParams: postgres.GenerateCardParams{
			UserIdentifier:     req.UserIdentifier,
			IinIdentifier:      req.IinCode,
			CardTypeIdentifier: req.CardType,
			ExpiresAt:          req.GetExpiresAt().AsTime(),
			ExpireMonth:        req.ExpireMonth,
			ExpireYear:         req.ExpireYear,
			Name:               user.Profile.FirstName + " " + user.Profile.LastName,
			Metadata:           []byte(req.MetaData),
		},
		AfterCreate: func(card postgres.Card) error {
			return nil
		},
	})

	if err != nil {
		return nil, err
	}

	return &pb.CardActionsTypeResponse{
		Error:   false,
		Message: "card created success",
		Card: &pb.Card{
			Id:             card.Card.ID,
			Identifier:     card.Card.Identifier,
			UserIdentifier: card.Card.UserIdentifier,
			Status:         string(card.Card.Status),
			Iin:            card.Card.Iin,
			CardType:       card.Card.CardType,
			IinCode:        card.Card.IinCode,
			CardTypeCode:   card.Card.CardTypeCode,
			AccountNumber:  card.Card.AccountNumber,
			LuhnDigit:      card.Card.LuhnDigit,
			Cvv:            card.Card.Cvv1,
			PinCode:        card.Card.PinCode,
			CardNumber:     card.Card.PrimaryAccountNumber,
			ExpireYear:     card.Card.ExpireYear,
			ExpireMonth:    card.Card.ExpireMonth,
			MetaData:       string(card.Card.MetaData),
			ExpiresAt:      timestamppb.New(card.Card.ExpiresAt.Time),
			CreatedAt:      timestamppb.New(card.Card.CreatedAt),
			UpdatedAt:      timestamppb.New(card.Card.UpdatedAt.Time),
			DeletedAt:      timestamppb.New(card.Card.DeletedAt.Time),
		},
	}, nil
}

// CreateCardType implements pb.CardAdministrationServiceServer.
func (s *administrationService) CreateCardType(ctx context.Context, req *pb.CreateCardTypeRequest) (*pb.CardTypeActionsResponse, error) {
	iin, err := s.repo.GetIssuerIdentifierByNumberAndStatus(ctx, postgres.GetIssuerIdentifierByNumberAndStatusParams{
		Iin:    fmt.Sprintf("%d", req.Iin),
		Status: postgres.IinStatusACTIVE,
	})
	if err != nil {
		return nil, err
	}

	cardType, err := s.repo.CreateCardType(ctx, postgres.CreateCardTypeParams{
		Identifier: ulid.Make().String(),
		Code:       req.Code,
		Name:       req.Name,
		Description: pgtype.Text{
			String: req.Description,
			Valid:  true,
		},
		Iin:      iin.ID,
		MetaData: []byte(req.MetaData),
	})

	if err != nil {
		return nil, err
	}

	return &pb.CardTypeActionsResponse{
		Error:   false,
		Message: "card type created successfully",
		CardType: &pb.CardType{
			Id:          cardType.ID,
			Identifier:  cardType.Identifier,
			Code:        cardType.Code,
			Name:        cardType.Name,
			Description: cardType.Description.String,
			MetaData:    string(cardType.MetaData),
			CreatedAt:   timestamppb.New(cardType.CreatedAt),
			UpdatedAt:   timestamppb.New(cardType.UpdatedAt.Time),
			DeletedAt:   timestamppb.New(cardType.DeletedAt.Time),
		},
	}, nil
}

// CreateIssuerIdentifier implements pb.CardAdministrationServiceServer.
func (s *administrationService) CreateIssuerIdentifier(ctx context.Context, req *pb.CreateIssuerIdentifierRequest) (*pb.IssuerIdentifierActionsResponse, error) {
	iin, err := s.repo.CreateIssuerIdentifier(ctx, postgres.CreateIssuerIdentifierParams{
		Identifier:           ulid.Make().String(),
		Iin:                  req.Iin,
		Status:               postgres.IinStatus(req.Status),
		IssuerUserIdentifier: req.IssuerUserIdentifier,
		IssuerName:           req.IssuerName,
		IssuerLogo: pgtype.Text{
			String: req.IssuerLogo,
			Valid:  true,
		},
		IssuerUrl: pgtype.Text{
			String: req.IssuerUrl,
			Valid:  true,
		},
		MetaData: []byte("{}"),
		DesKey:   DEFAULT_DES_KEY,
	})

	if err != nil {
		return nil, err
	}

	return &pb.IssuerIdentifierActionsResponse{
		Error:   false,
		Message: "issuer identifier created successfully",
		IssuerIdentifier: &pb.IssuerIdentifier{
			Id:               iin.ID,
			Identifier:       iin.Identifier,
			Iin:              iin.Iin,
			Status:           string(iin.Status),
			IssuerIdentifier: iin.IssuerUserIdentifier,
			IssuerName:       iin.IssuerName,
			IssuerLogo:       iin.IssuerLogo.String,
			IssuerUrl:        iin.IssuerUrl.String,
			MetaData:         string(iin.MetaData),
			ExpireDate:       timestamppb.New(iin.ExpiresAt.Time),
			CreatedAt:        timestamppb.New(iin.CreatedAt),
			UpdatedAt:        timestamppb.New(iin.UpdatedAt.Time),
			DeletedAt:        timestamppb.New(iin.DeletedAt.Time),
		},
	}, nil
}

// SetCardMetaData implements pb.CardAdministrationServiceServer.
func (s *administrationService) SetCardMetaData(ctx context.Context, req *pb.SetCardMetaDataRequest) (*pb.CardActionsTypeResponse, error) {
	card, err := s.repo.GetCardByIdentifier(ctx, req.Identifier)
	if err != nil {
		return nil, err
	}

	card.MetaData = []byte(req.MetaData)
	card, err = s.repo.UpdateCard(ctx, postgres.UpdateCardParams{
		Identifier: req.Identifier,
		Status:     card.Status,
		MetaData:   []byte(req.MetaData),
	})
	if err != nil {
		return nil, err
	}

	dto, err := structure.ConvertStruct[*pb.Card](card)
	if err != nil {
		return nil, err
	}

	return &pb.CardActionsTypeResponse{
		Error:   false,
		Message: "card meta data updated successfully",
		Card:    dto,
	}, nil
}

// SetCardPIN implements pb.CardAdministrationServiceServer.
func (s *administrationService) SetCardPIN(ctx context.Context, req *pb.SetCardPINRequest) (*pb.CardActionsTypeResponse, error) {
	card, err := s.repo.GetCardByIdentifier(ctx, req.Identifier)
	if err != nil {
		return nil, err
	}

	// hash plain pin code string
	pinBcrypt, err := stringutil.HashString(req.PinCode)
	if err != nil {
		return nil, err
	}

	card, err = s.repo.UpdateCardPIN(ctx, postgres.UpdateCardPINParams{
		Identifier: card.Identifier,
		PinCode:    pinBcrypt,
	})

	if err != nil {
		return nil, err
	}

	return &pb.CardActionsTypeResponse{
		Error:   false,
		Message: "card pin code updated success",
		Card: &pb.Card{
			Id:             card.ID,
			Identifier:     card.Identifier,
			UserIdentifier: card.UserIdentifier,
			Status:         string(card.Status),
			Iin:            card.Iin,
			CardType:       card.CardType,
			IinCode:        card.IinCode,
			CardTypeCode:   card.CardTypeCode,
			AccountNumber:  card.AccountNumber,
			LuhnDigit:      card.LuhnDigit,
			Cvv:            card.Cvv2,
			PinCode:        card.PinCode,
			CardNumber:     card.PrimaryAccountNumber,
			ExpireYear:     card.ExpireYear,
			ExpireMonth:    card.ExpireMonth,
			MetaData:       string(card.MetaData),
			ExpiresAt:      timestamppb.New(card.ExpiresAt.Time),
			CreatedAt:      timestamppb.New(card.CreatedAt),
			UpdatedAt:      timestamppb.New(card.UpdatedAt.Time),
			DeletedAt:      timestamppb.New(card.DeletedAt.Time),
		},
	}, nil
}

// SetCardStatus implements pb.CardAdministrationServiceServer.
func (s *administrationService) SetCardStatus(ctx context.Context, req *pb.SetCardStatusRequest) (*pb.CardActionsTypeResponse, error) {
	card, err := s.repo.GetCardByIdentifier(ctx, req.Identifier)
	if err != nil {
		return nil, err
	}

	card, err = s.repo.UpdateCard(ctx, postgres.UpdateCardParams{
		Identifier: req.Identifier,
		Status:     postgres.CardStatus(req.Status),
		MetaData:   card.MetaData,
	})
	if err != nil {
		return nil, err
	}

	return &pb.CardActionsTypeResponse{
		Error:   false,
		Message: "card status updated successfully",
		Card: &pb.Card{
			Id:             card.ID,
			Identifier:     card.Identifier,
			UserIdentifier: card.UserIdentifier,
			Status:         string(card.Status),
			Iin:            card.Iin,
			CardType:       card.CardType,
			IinCode:        card.IinCode,
			CardTypeCode:   card.CardTypeCode,
			AccountNumber:  card.AccountNumber,
			LuhnDigit:      card.LuhnDigit,
			Cvv:            card.Cvv2,
			PinCode:        card.PinCode,
			CardNumber:     card.PrimaryAccountNumber,
			ExpireYear:     card.ExpireYear,
			ExpireMonth:    card.ExpireMonth,
			MetaData:       string(card.MetaData),
			ExpiresAt:      timestamppb.New(card.ExpiresAt.Time),
			CreatedAt:      timestamppb.New(card.CreatedAt),
			UpdatedAt:      timestamppb.New(card.UpdatedAt.Time),
			DeletedAt:      timestamppb.New(card.DeletedAt.Time),
		},
	}, nil
}

// UpdateCardType implements pb.CardAdministrationServiceServer.
func (s *administrationService) UpdateCardType(ctx context.Context, req *pb.UpdateCardTypeRequest) (*pb.CardTypeActionsResponse, error) {
	cardType, err := s.repo.GetCardTypeByCode(ctx, req.Code)
	if err != nil {
		return nil, err
	}

	cardType, err = s.repo.UpdateCardType(ctx, postgres.UpdateCardTypeParams{
		Code: req.Code,
		Name: req.Name,
		Description: pgtype.Text{
			String: req.Description,
			Valid:  true,
		},
		MetaData: []byte(req.MetaData),
	})
	if err != nil {
		return nil, err
	}

	return &pb.CardTypeActionsResponse{
		Error:   false,
		Message: "card type updated successfully",
		CardType: &pb.CardType{
			Id:          cardType.ID,
			Identifier:  cardType.Identifier,
			Code:        cardType.Code,
			Name:        cardType.Name,
			Description: cardType.Description.String,
			MetaData:    string(cardType.MetaData),
			CreatedAt:   timestamppb.New(cardType.CreatedAt),
			UpdatedAt:   timestamppb.New(cardType.UpdatedAt.Time),
			DeletedAt:   timestamppb.New(cardType.DeletedAt.Time),
		},
	}, nil
}

// UpdateIssuerIdentifier implements pb.CardAdministrationServiceServer.
func (s *administrationService) UpdateIssuerIdentifier(ctx context.Context, req *pb.UpdateIssuerIdentifierRequest) (*pb.IssuerIdentifierActionsResponse, error) {
	iin, err := s.repo.GetIssuerIdentifierByIdentifier(ctx, postgres.GetIssuerIdentifierByIdentifierParams{
		Identifier: req.Identifier,
		Status:     postgres.IinStatusACTIVE,
	})
	if err != nil {
		return nil, err
	}

	iin, err = s.repo.UpdateIssuerIdentifier(ctx, postgres.UpdateIssuerIdentifierParams{
		Identifier: req.Identifier,
		Status:     postgres.IinStatus(req.Status),
		IssuerName: req.IssuerName,
		IssuerLogo: pgtype.Text{
			String: req.IssuerLogo,
			Valid:  true,
		},
		IssuerUrl: pgtype.Text{
			String: req.IssuerUrl,
			Valid:  true,
		},
		MetaData: []byte(req.MetaData),
		ExpiresAt: pgtype.Timestamptz{
			Time:  req.ExpireDate.AsTime(),
			Valid: true,
		},
	})
	if err != nil {
		return nil, err
	}

	return &pb.IssuerIdentifierActionsResponse{
		Error:   false,
		Message: "issuer identifier updated successfully",
		IssuerIdentifier: &pb.IssuerIdentifier{
			Id:               iin.ID,
			Identifier:       iin.Identifier,
			Iin:              iin.Iin,
			Status:           string(iin.Status),
			IssuerIdentifier: iin.IssuerUserIdentifier,
			IssuerName:       iin.IssuerName,
			IssuerLogo:       iin.IssuerLogo.String,
			IssuerUrl:        iin.IssuerUrl.String,
			MetaData:         string(iin.MetaData),
			ExpireDate:       timestamppb.New(iin.ExpiresAt.Time),
			CreatedAt:        timestamppb.New(iin.CreatedAt),
			UpdatedAt:        timestamppb.New(iin.UpdatedAt.Time),
			DeletedAt:        timestamppb.New(iin.DeletedAt.Time),
		},
	}, nil
}

// ChargeCardAccountBalance implements pb.CardAdministrationServiceServer.
func (s *administrationService) ChargeCardAccountBalance(ctx context.Context, req *pb.ChargeCardAccountBalanceRequest) (*pb.ChargeAccountBalanceResponse, error) {
	card, err := s.repo.GetCardByPAN(ctx, req.PrimaryAccountNumber)
	if err != nil {
		return nil, err
	}

	// check if card is active
	if card.Status != postgres.CardStatusACTIVE {
		return nil, status.Errorf(codes.InvalidArgument, "card is not active")
	}

	// check if card is expired
	if card.ExpiresAt.Time.Before(time.Now().UTC()) {
		return nil, status.Errorf(codes.InvalidArgument, "card is expired")
	}

	account := fmt.Sprintf(s.config.DefaultCardCreditAccountPattern,
		card.UserIdentifier,
		card.IinCode,
		card.CardTypeCode,
		card.AccountNumber,
		card.LuhnDigit,
	)

	// create a multiple posting transaction s.config.DefaultCardCreditAccountPattern)

	// create a multiple posting transaction
	tx := operations.V2CreateTransactionRequest{
		V2PostTransaction: shared.V2PostTransaction{
			Metadata: map[string]string{
				"charger":     "SUPER_ADMIN",
				"payment_id":  req.PaymentId,
				"description": req.Description,
			},
			Postings: []shared.V2Posting{
				{
					Amount:      big.NewInt(int64(req.Amount)),
					Asset:       req.Asset,
					Source:      "world",
					Destination: account,
				},
			},
			Reference: formancesdkgo.String(req.Reference),
			// @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ SCRIPT TEST @@@@@@@@
			// Script: &shared.V2PostTransactionScript{
			// 	Plain: "vars {\n" +
			// 		"account $user\n" +
			// 		"}\n" +
			// 		"send [IRR 10] (\n" +
			// 		"	source = @world\n" +
			// 		"	destination = $user\n" +
			// 		")\n" +
			// 		"",
			// 	Vars: map[string]string{
			// 		"user": "users:042",
			// 	},
			// },
			// @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ SCRIPT TEST @@@@@@@@
		},
		DryRun: formancesdkgo.Bool(false),
		Force:  formancesdkgo.Bool(false),
		Ledger: s.config.DefaultNetworkCreditLedger,
	}

	txRes, err := transaction.CreateTransaction(ctx, s.formance, tx)
	if err != nil {
		return nil, fmt.Errorf("create transaction error: %v", err)
	}

	return &pb.ChargeAccountBalanceResponse{
		Error:         false,
		Message:       "account charged success",
		TransactionId: txRes.GetV2CreateTransactionResponse().GetData().ID.String(),
		PaymentId:     req.PaymentId,
		Reference:     *txRes.GetV2CreateTransactionResponse().GetData().Reference,
		Description:   req.Description,
		Amount:        strconv.Itoa(int(req.Amount)),
		Asset:         req.Asset,
	}, nil

}

func translateDefaultCardCreditAccount(card *postgres.Card, pattern string) (address string) {
	// default pattern is @user_id:@iin:@card_type:@account_number:credit:default:IRR
	address = strings.ReplaceAll("@user_id", card.UserIdentifier, pattern)
	address = strings.ReplaceAll("@iin", card.IinCode, address)
	address = strings.ReplaceAll("@card_type", card.CardTypeCode, address)
	address = strings.ReplaceAll("@account_number", card.AccountNumber, address)
	return address
}
