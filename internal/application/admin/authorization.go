package admin

import (
	"context"
	"errors"

	"github.com/liveutil/card_service/pb"
	"github.com/liveutil/go-lib/contextutil"
	"github.com/liveutil/go-lib/servicemesh"
	"github.com/thoas/go-funk"
	"go.opentelemetry.io/otel/trace"
)

type authorizationMiddleware struct {
	pb.UnimplementedCardAdministrationServiceServer
	// next is the next service in the middleware chain
	next pb.CardAdministrationServiceServer

	users  servicemesh.UserMeshService
	tracer trace.Tracer
}

// NewAuthorizationMiddleware returns new authorization layer for pb.CardServiceServer
func NewAuthorizationMiddleware(users servicemesh.UserMeshService, tracer trace.Tracer, service pb.CardAdministrationServiceServer) pb.CardAdministrationServiceServer {
	return &authorizationMiddleware{
		next:   service,
		users:  users,
		tracer: tracer,
	}
}

// CreateCard implements pb.CardAdministrationServiceServer.
func (a *authorizationMiddleware) CreateCard(ctx context.Context, req *pb.CreateCardRequest) (*pb.CardActionsTypeResponse, error) {
	user := &servicemesh.UserModel{}

	err := contextutil.CatchUser(ctx, user)
	if err != nil {
		return nil, err
	}

	user, err = a.users.GetSafeUserByID(ctx, user.ID)
	if err != nil {
		return nil, err
	}

	if !funk.Contains(user.Roles, "ADMIN") || !funk.Contains(user.Roles, "SUPER_ADMIN") {
		return nil, err
	}

	return a.next.CreateCard(ctx, req)
}

// CreateCardType implements pb.CardAdministrationServiceServer.
func (a *authorizationMiddleware) CreateCardType(ctx context.Context, req *pb.CreateCardTypeRequest) (*pb.CardTypeActionsResponse, error) {
	user := &servicemesh.UserModel{}

	err := contextutil.CatchUser(ctx, user)
	if err != nil {
		return nil, err
	}

	user, err = a.users.GetSafeUserByID(ctx, user.ID)
	if err != nil {
		return nil, err
	}

	if !funk.Contains(user.Roles, "ADMIN") || !funk.Contains(user.Roles, "SUPER_ADMIN") {
		return nil, err
	}

	return a.next.CreateCardType(ctx, req)
}

// CreateIssuerIdentifier implements pb.CardAdministrationServiceServer.
func (a *authorizationMiddleware) CreateIssuerIdentifier(ctx context.Context, req *pb.CreateIssuerIdentifierRequest) (*pb.IssuerIdentifierActionsResponse, error) {
	user := &servicemesh.UserModel{}

	err := contextutil.CatchUser(ctx, user)
	if err != nil {
		return nil, err
	}

	user, err = a.users.GetSafeUserByID(ctx, user.ID)
	if err != nil {
		return nil, err
	}

	if !funk.Contains(user.Roles, "ADMIN") || !funk.Contains(user.Roles, "SUPER_ADMIN") {
		return nil, errors.New("permission denied")
	}

	return a.next.CreateIssuerIdentifier(ctx, req)
}

// SetCardMetaData implements pb.CardAdministrationServiceServer.
func (a *authorizationMiddleware) SetCardMetaData(ctx context.Context, req *pb.SetCardMetaDataRequest) (*pb.CardActionsTypeResponse, error) {
	user := &servicemesh.UserModel{}

	err := contextutil.CatchUser(ctx, user)
	if err != nil {
		return nil, err
	}

	user, err = a.users.GetSafeUserByID(ctx, user.ID)
	if err != nil {
		return nil, err
	}

	if !funk.Contains(user.Roles, "ADMIN") || !funk.Contains(user.Roles, "SUPER_ADMIN") {
		return nil, err
	}

	return a.next.SetCardMetaData(ctx, req)
}

// SetCardPIN implements pb.CardAdministrationServiceServer.
func (a *authorizationMiddleware) SetCardPIN(ctx context.Context, req *pb.SetCardPINRequest) (*pb.CardActionsTypeResponse, error) {
	user := &servicemesh.UserModel{}

	err := contextutil.CatchUser(ctx, user)
	if err != nil {
		return nil, err
	}

	user, err = a.users.GetSafeUserByID(ctx, user.ID)
	if err != nil {
		return nil, err
	}

	if !funk.Contains(user.Roles, "ADMIN") || !funk.Contains(user.Roles, "SUPER_ADMIN") {
		return nil, err
	}

	return a.next.SetCardPIN(ctx, req)
}

// SetCardStatus implements pb.CardAdministrationServiceServer.
func (a *authorizationMiddleware) SetCardStatus(ctx context.Context, req *pb.SetCardStatusRequest) (*pb.CardActionsTypeResponse, error) {
	user := &servicemesh.UserModel{}

	err := contextutil.CatchUser(ctx, user)
	if err != nil {
		return nil, err
	}

	user, err = a.users.GetSafeUserByID(ctx, user.ID)
	if err != nil {
		return nil, err
	}

	if !funk.Contains(user.Roles, "ADMIN") || !funk.Contains(user.Roles, "SUPER_ADMIN") {
		return nil, err
	}

	return a.next.SetCardStatus(ctx, req)
}

// UpdateCardType implements pb.CardAdministrationServiceServer.
func (a *authorizationMiddleware) UpdateCardType(ctx context.Context, req *pb.UpdateCardTypeRequest) (*pb.CardTypeActionsResponse, error) {
	user := &servicemesh.UserModel{}

	err := contextutil.CatchUser(ctx, user)
	if err != nil {
		return nil, err
	}

	user, err = a.users.GetSafeUserByID(ctx, user.ID)
	if err != nil {
		return nil, err
	}

	if !funk.Contains(user.Roles, "ADMIN") || !funk.Contains(user.Roles, "SUPER_ADMIN") {
		return nil, err
	}

	return a.next.UpdateCardType(ctx, req)
}

// UpdateIssuerIdentifier implements pb.CardAdministrationServiceServer.
func (a *authorizationMiddleware) UpdateIssuerIdentifier(ctx context.Context, req *pb.UpdateIssuerIdentifierRequest) (*pb.IssuerIdentifierActionsResponse, error) {
	user := &servicemesh.UserModel{}

	err := contextutil.CatchUser(ctx, user)
	if err != nil {
		return nil, err
	}

	user, err = a.users.GetSafeUserByID(ctx, user.ID)
	if err != nil {
		return nil, err
	}

	if !funk.Contains(user.Roles, "ADMIN") || !funk.Contains(user.Roles, "SUPER_ADMIN") {
		return nil, err
	}

	return a.next.UpdateIssuerIdentifier(ctx, req)
}

// ChargeCardAccountBalance implements pb.CardAdministrationServiceServer.
func (a *authorizationMiddleware) ChargeCardAccountBalance(ctx context.Context, req *pb.ChargeCardAccountBalanceRequest) (*pb.ChargeAccountBalanceResponse, error) {
	return a.next.ChargeCardAccountBalance(ctx, req)
}
