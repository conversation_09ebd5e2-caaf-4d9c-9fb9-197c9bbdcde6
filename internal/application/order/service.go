package order

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/liveutil/go-lib/contextutil"
	"github.com/liveutil/go-lib/paseto"
	"github.com/liveutil/go-lib/servicemesh"
	"github.com/liveutil/order_service/internal/config"
	"github.com/liveutil/order_service/internal/infrastructure/db/postgres"
	"github.com/liveutil/order_service/pb"
	"github.com/oklog/ulid/v2"
	"go.opentelemetry.io/otel/trace"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type service struct {
	pb.UnimplementedOrderServiceServer

	repo   postgres.Store
	paseto paseto.Maker
	config *config.Configuration
	users  servicemesh.UserMeshService
	cards  servicemesh.CardMeshService
	tracer trace.Tracer
}

// OrderServiceServer defines options for creating a new pb.OrderServiceServer.
func NewService(opts *OrderServiceOpts) pb.OrderServiceServer {
	return &service{
		repo:   opts.Repository,
		config: opts.Config,
		paseto: opts.PASETO,
		users:  opts.UsersServiceMesh,
		cards:  opts.CardsServiceMesh,
		tracer: opts.Tracer,
	}
}

// CancelOrder implements pb.OrderServiceServer.
func (s *service) CancelOrder(ctx context.Context, req *pb.OrderActionRequest) (*pb.OrderActionsResponse, error) {
	user := &servicemesh.UserModel{}

	err := contextutil.CatchUser(ctx, user)
	if err != nil {
		return nil, err
	}

	user, err = s.users.GetSafeUserByID(ctx, user.ID)
	if err != nil {
		return nil, err
	}

	order, err := s.repo.GetOrderByReference(ctx, req.Reference)
	if err != nil {
		return nil, err
	}

	if order.UserIdentifier != user.Identifier {
		return nil, ErrOrderNotBelongsToUser
	}

	txResult, err := s.repo.UpdateOrderStatusTx(ctx, postgres.UpdateOrderStatusTxParams{
		Reference: req.Reference,
		Status:    postgres.OrderStatusUSERCANCELLED,
		AfterUpdate: func(order postgres.Order) error {
			// ToDo: Send Notification
			return nil
		},
	})
	if err != nil {
		return nil, err
	}

	result := &pb.Order{
		Identifier:       txResult.Order.Identifier,
		Status:           string(txResult.Order.Status),
		UserIdentifier:   user.Identifier,
		CardIdentifier:   order.CardIdentifier,
		CardHolderName:   order.CardHolderName,
		CardPan:          order.CardPan,
		TargetDomain:     order.TargetDomain,
		OrderType:        order.OrderType,
		OrderAmount:      order.OrderAmount,
		OrderAsset:       order.OrderAsset,
		OrderDescription: order.OrderDescription,
		Reference:        order.Reference,
		VoucherCode:      order.VoucherCode,
		CreatedAt:        timestamppb.New(txResult.Order.CreatedAt),
		UpdatedAt:        timestamppb.New(txResult.Order.UpdatedAt.Time),
		DeletedAt:        timestamppb.New(txResult.Order.DeletedAt.Time),
	}

	return &pb.OrderActionsResponse{
		Error:   false,
		Message: "order cancelled successfully",
		Order:   result,
	}, nil
}

// ContextUserOrders implements pb.OrderServiceServer.
func (s *service) ContextUserOrders(ctx context.Context, req *pb.ContextUserOrdersRequest) (*pb.ContextUserOrdersResponse, error) {
	user := &servicemesh.UserModel{}

	err := contextutil.CatchUser(ctx, user)
	if err != nil {
		return nil, err
	}

	user, err = s.users.GetSafeUserByID(ctx, user.ID)
	if err != nil {
		return nil, err
	}

	orders, err := s.repo.ListUserOrders(ctx, postgres.ListUserOrdersParams{
		UserIdentifier: user.Identifier,
		Limit:          req.PageSize,
		Offset:         req.PageOffset,
	})
	if err != nil {
		return nil, err
	}

	result := &pb.ContextUserOrdersResponse{
		Error:        false,
		Message:      "context user orders list fetched success",
		ResultLength: int32(len(orders)),
		Orders:       []*pb.Order{},
	}

	for _, order := range orders {
		result.Orders = append(result.Orders, &pb.Order{
			Identifier:       order.Identifier,
			UserIdentifier:   order.UserIdentifier,
			CardIdentifier:   order.CardIdentifier,
			CardHolderName:   order.CardHolderName,
			CardPan:          order.CardPan,
			TargetDomain:     order.TargetDomain,
			OrderType:        order.OrderType,
			OrderAmount:      order.OrderAmount,
			OrderAsset:       order.OrderAsset,
			OrderDescription: order.OrderDescription,
			Reference:        order.Reference,
			VoucherCode:      order.VoucherCode,
			Status:           string(order.Status),
			CreatedAt:        timestamppb.New(order.CreatedAt),
			UpdatedAt:        timestamppb.New(order.UpdatedAt.Time),
			DeletedAt:        timestamppb.New(order.DeletedAt.Time),
		})
	}

	return result, nil
}

// GetOrder implements pb.OrderServiceServer.
func (s *service) GetOrder(ctx context.Context, req *pb.OrderActionRequest) (*pb.OrderActionsResponse, error) {
	user := &servicemesh.UserModel{}

	err := contextutil.CatchUser(ctx, user)
	if err != nil {
		return nil, err
	}

	user, err = s.users.GetSafeUserByID(ctx, user.ID)
	if err != nil {
		return nil, err
	}

	order, err := s.repo.GetUserOrderByReference(ctx, postgres.GetUserOrderByReferenceParams{
		Reference:      req.Reference,
		UserIdentifier: user.Identifier,
	})
	if err != nil {
		return nil, err
	}

	result := &pb.Order{
		Identifier:       order.Identifier,
		UserIdentifier:   order.UserIdentifier,
		CardIdentifier:   order.CardIdentifier,
		CardHolderName:   order.CardHolderName,
		CardPan:          order.CardPan,
		TargetDomain:     order.TargetDomain,
		OrderType:        order.OrderType,
		OrderAmount:      order.OrderAmount,
		Status:           string(order.Status),
		OrderAsset:       order.OrderAsset,
		OrderDescription: order.OrderDescription,
		Reference:        order.Reference,
		VoucherCode:      order.VoucherCode,
		CreatedAt:        timestamppb.New(order.CreatedAt),
		UpdatedAt:        timestamppb.New(order.UpdatedAt.Time),
		DeletedAt:        timestamppb.New(order.DeletedAt.Time),
	}

	return &pb.OrderActionsResponse{
		Error:   false,
		Message: "success",
		Order:   result,
	}, nil
}

// PayOrder implements pb.OrderServiceServer.
func (s *service) PayOrder(ctx context.Context, req *pb.PayOrderRequest) (*pb.PayOrderResponse, error) {
	panic("unimplemented")
}

// PlaceOrder implements pb.OrderServiceServer.
func (s *service) PlaceOrder(ctx context.Context, req *pb.PlaceOrderRequest) (*pb.OrderActionsResponse, error) {
	user := &servicemesh.UserModel{}

	err := contextutil.CatchUser(ctx, user)
	if err != nil {
		return nil, err
	}

	user, err = s.users.GetSafeUserByID(ctx, user.ID)
	if err != nil {
		return nil, err
	}

	card, err := s.cards.GetSafeCardByCardNumber(ctx, req.CardPan)
	if err != nil {
		return nil, err
	}

	if card.UserIdentifier != user.Identifier {
		return nil, ErrCardNotBelongsToUser
	}

	if card.ExpiresAt.Before(time.Now()) {
		return nil, ErrCardExpired
	}

	if card.Status != "ACTIVE" {
		return nil, ErrCardNotActive
	}

	discount := "0"

	if len(strings.TrimSpace(req.VoucherCode)) != 0 {
		// check voucher is valid
		voucher, err := s.repo.GetVoucherByCode(ctx, req.VoucherCode)
		if err != nil {
			return nil, ErrVoucherExpiredOrNotFound
		}

		// check voucher is not used by user
		if voucher.PerUserMaxUsage > 0 {
			count, err := s.repo.CountUserUsedVoucher(ctx, postgres.CountUserUsedVoucherParams{
				VoucherCode:    req.VoucherCode,
				UserIdentifier: user.Identifier,
			})
			if err != nil {
				return nil, err
			}

			if count >= int64(voucher.PerUserMaxUsage) {
				return nil, ErrVoucherMaxUsageExceeded
			}

			discount = voucher.DiscountPercentage
		}
	}

	order, err := s.repo.UpsertOrderTx(ctx, postgres.UpsertOrderTxParams{
		UpsertNewOrderParams: postgres.UpsertNewOrderParams{
			Identifier:       ulid.Make().String(),
			Reference:        req.Reference,
			UserIdentifier:   user.Identifier,
			CardIdentifier:   card.Identifier,
			CardHolderName:   fmt.Sprintf("%s %s", user.Profile.FirstName, user.Profile.LastName),
			CardPan:          card.CardNumber,
			TargetDomain:     req.TargetDomain,
			Status:           "BOOKED",
			OrderType:        req.TargetDomain,
			OrderAmount:      req.Amount,
			OrderAsset:       req.Asset,
			OrderDescription: req.Description,
			Reference_2:      req.Reference,
			VoucherCode:      req.VoucherCode,
			MetaData:         []byte(`{"discount": "` + discount + `", "voucher_code": "` + req.VoucherCode + `"}`),
		},
		AfterUpsert: func(order postgres.Order) error {
			return nil
		}})
	if err != nil {
		return nil, err
	}

	result := &pb.Order{
		Identifier:       order.Order.Identifier,
		UserIdentifier:   order.Order.UserIdentifier,
		CardIdentifier:   order.Order.CardIdentifier,
		CardHolderName:   order.Order.CardHolderName,
		CardPan:          order.Order.CardPan,
		TargetDomain:     order.Order.TargetDomain,
		OrderType:        order.Order.OrderType,
		OrderAmount:      order.Order.OrderAmount,
		Status:           string(order.Order.Status),
		OrderAsset:       order.Order.OrderAsset,
		OrderDescription: order.Order.OrderDescription,
		Reference:        order.Order.Reference,
		VoucherCode:      order.Order.VoucherCode,
		CreatedAt:        timestamppb.New(order.Order.CreatedAt),
		UpdatedAt:        timestamppb.New(order.Order.UpdatedAt.Time),
		DeletedAt:        timestamppb.New(order.Order.DeletedAt.Time),
	}

	return &pb.OrderActionsResponse{
		Error:   false,
		Message: "success",
		Order:   result,
	}, nil
}
