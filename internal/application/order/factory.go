package order

import (
	formancesdkgo "github.com/formancehq/formance-sdk-go/v3"
	kitlog "github.com/go-kit/log"
	"github.com/liveutil/go-lib/paseto"
	"github.com/liveutil/go-lib/servicemesh"
	"github.com/liveutil/order_service/internal/config"
	"github.com/liveutil/order_service/internal/infrastructure/db/postgres"
	"github.com/liveutil/order_service/pb"
	"github.com/nats-io/nats.go"
	"github.com/redis/go-redis/v9"
	"go.opentelemetry.io/otel/trace"
)

type OrderServiceOpts struct {
	Repository       postgres.Store
	Config           *config.Configuration
	Redis            *redis.Client
	NATS             *nats.Conn
	UsersServiceMesh servicemesh.UserMeshService
	CardsServiceMesh servicemesh.CardMeshService
	Logger           kitlog.Logger
	PASETO           paseto.Maker
	SchemaPath       string
	ApplicationName  string
	Tracer           trace.Tracer
	FormanceClient   *formancesdkgo.Formance
}

// NewOrderService creates a new user service with all middleware layers
func NewOrderService(opts *OrderServiceOpts) (pb.OrderServiceServer, error) {
	// Create base service
	svc := NewService(opts)

	// Add middleware layers
	svc = NewAuthorizationMiddleware(svc)

	return svc, nil
}
