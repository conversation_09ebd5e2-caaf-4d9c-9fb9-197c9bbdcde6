package order

import (
	"context"

	"github.com/liveutil/order_service/pb"
)

type authorizationMiddleware struct {
	pb.UnimplementedOrderServiceServer
	// next is the next service in the middleware chain
	next pb.OrderServiceServer
}

// NewAuthorizationMiddleware returns new authorization layer for pb.OrderServiceServer
func NewAuthorizationMiddleware(service pb.OrderServiceServer) pb.OrderServiceServer {
	return &authorizationMiddleware{
		next: service,
	}
}

// CancelOrder implements pb.OrderServiceServer.
func (a *authorizationMiddleware) CancelOrder(ctx context.Context, req *pb.OrderActionRequest) (*pb.OrderActionsResponse, error) {
	return a.next.CancelOrder(ctx, req)
}

// ContextUserOrders implements pb.OrderServiceServer.
func (a *authorizationMiddleware) ContextUserOrders(ctx context.Context, req *pb.ContextUserOrdersRequest) (*pb.ContextUserOrdersResponse, error) {
	return a.next.ContextUserOrders(ctx, req)
}

// GetOrder implements pb.OrderServiceServer.
func (a *authorizationMiddleware) GetOrder(ctx context.Context, req *pb.OrderActionRequest) (*pb.OrderActionsResponse, error) {
	return a.next.GetOrder(ctx, req)
}

// PayOrder implements pb.OrderServiceServer.
func (a *authorizationMiddleware) PayOrder(ctx context.Context, req *pb.PayOrderRequest) (*pb.PayOrderResponse, error) {
	return a.next.PayOrder(ctx, req)
}

// PlaceOrder implements pb.OrderServiceServer.
func (a *authorizationMiddleware) PlaceOrder(ctx context.Context, req *pb.PlaceOrderRequest) (*pb.OrderActionsResponse, error) {
	return a.next.PlaceOrder(ctx, req)
}
