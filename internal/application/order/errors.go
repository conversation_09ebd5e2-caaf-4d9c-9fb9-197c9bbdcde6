package order

import "errors"

var (
	ErrCardNotBelongsToUser     = errors.New("card does not belong to user")
	ErrCardExpired              = errors.New("card is expired")
	ErrCardNotActive            = errors.New("card is not active")
	ErrVoucherExpiredOrNotFound = errors.New("voucher is expired or not found")
	ErrVoucherMaxUsageExceeded  = errors.New("voucher max usage exceeded")
	ErrOrderNotBelongsToUser    = errors.New("order does not belong to user")
)
