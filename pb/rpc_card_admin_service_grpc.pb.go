// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: rpc_card_admin_service.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	CardAdministrationService_CreateIssuerIdentifier_FullMethodName   = "/pb.CardAdministrationService/CreateIssuerIdentifier"
	CardAdministrationService_UpdateIssuerIdentifier_FullMethodName   = "/pb.CardAdministrationService/UpdateIssuerIdentifier"
	CardAdministrationService_CreateCardType_FullMethodName           = "/pb.CardAdministrationService/CreateCardType"
	CardAdministrationService_UpdateCardType_FullMethodName           = "/pb.CardAdministrationService/UpdateCardType"
	CardAdministrationService_CreateCard_FullMethodName               = "/pb.CardAdministrationService/CreateCard"
	CardAdministrationService_SetCardStatus_FullMethodName            = "/pb.CardAdministrationService/SetCardStatus"
	CardAdministrationService_SetCardMetaData_FullMethodName          = "/pb.CardAdministrationService/SetCardMetaData"
	CardAdministrationService_SetCardPIN_FullMethodName               = "/pb.CardAdministrationService/SetCardPIN"
	CardAdministrationService_ChargeCardAccountBalance_FullMethodName = "/pb.CardAdministrationService/ChargeCardAccountBalance"
)

// CardAdministrationServiceClient is the client API for CardAdministrationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Card Administration Service API for managing user cards and related operations.
type CardAdministrationServiceClient interface {
	// Create a new issuer identifier
	CreateIssuerIdentifier(ctx context.Context, in *CreateIssuerIdentifierRequest, opts ...grpc.CallOption) (*IssuerIdentifierActionsResponse, error)
	// Update an existing issuer identifier
	UpdateIssuerIdentifier(ctx context.Context, in *UpdateIssuerIdentifierRequest, opts ...grpc.CallOption) (*IssuerIdentifierActionsResponse, error)
	CreateCardType(ctx context.Context, in *CreateCardTypeRequest, opts ...grpc.CallOption) (*CardTypeActionsResponse, error)
	UpdateCardType(ctx context.Context, in *UpdateCardTypeRequest, opts ...grpc.CallOption) (*CardTypeActionsResponse, error)
	// Create a new card for a user
	CreateCard(ctx context.Context, in *CreateCardRequest, opts ...grpc.CallOption) (*CardActionsTypeResponse, error)
	SetCardStatus(ctx context.Context, in *SetCardStatusRequest, opts ...grpc.CallOption) (*CardActionsTypeResponse, error)
	SetCardMetaData(ctx context.Context, in *SetCardMetaDataRequest, opts ...grpc.CallOption) (*CardActionsTypeResponse, error)
	SetCardPIN(ctx context.Context, in *SetCardPINRequest, opts ...grpc.CallOption) (*CardActionsTypeResponse, error)
	ChargeCardAccountBalance(ctx context.Context, in *ChargeCardAccountBalanceRequest, opts ...grpc.CallOption) (*ChargeAccountBalanceResponse, error)
}

type cardAdministrationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCardAdministrationServiceClient(cc grpc.ClientConnInterface) CardAdministrationServiceClient {
	return &cardAdministrationServiceClient{cc}
}

func (c *cardAdministrationServiceClient) CreateIssuerIdentifier(ctx context.Context, in *CreateIssuerIdentifierRequest, opts ...grpc.CallOption) (*IssuerIdentifierActionsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(IssuerIdentifierActionsResponse)
	err := c.cc.Invoke(ctx, CardAdministrationService_CreateIssuerIdentifier_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardAdministrationServiceClient) UpdateIssuerIdentifier(ctx context.Context, in *UpdateIssuerIdentifierRequest, opts ...grpc.CallOption) (*IssuerIdentifierActionsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(IssuerIdentifierActionsResponse)
	err := c.cc.Invoke(ctx, CardAdministrationService_UpdateIssuerIdentifier_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardAdministrationServiceClient) CreateCardType(ctx context.Context, in *CreateCardTypeRequest, opts ...grpc.CallOption) (*CardTypeActionsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CardTypeActionsResponse)
	err := c.cc.Invoke(ctx, CardAdministrationService_CreateCardType_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardAdministrationServiceClient) UpdateCardType(ctx context.Context, in *UpdateCardTypeRequest, opts ...grpc.CallOption) (*CardTypeActionsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CardTypeActionsResponse)
	err := c.cc.Invoke(ctx, CardAdministrationService_UpdateCardType_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardAdministrationServiceClient) CreateCard(ctx context.Context, in *CreateCardRequest, opts ...grpc.CallOption) (*CardActionsTypeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CardActionsTypeResponse)
	err := c.cc.Invoke(ctx, CardAdministrationService_CreateCard_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardAdministrationServiceClient) SetCardStatus(ctx context.Context, in *SetCardStatusRequest, opts ...grpc.CallOption) (*CardActionsTypeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CardActionsTypeResponse)
	err := c.cc.Invoke(ctx, CardAdministrationService_SetCardStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardAdministrationServiceClient) SetCardMetaData(ctx context.Context, in *SetCardMetaDataRequest, opts ...grpc.CallOption) (*CardActionsTypeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CardActionsTypeResponse)
	err := c.cc.Invoke(ctx, CardAdministrationService_SetCardMetaData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardAdministrationServiceClient) SetCardPIN(ctx context.Context, in *SetCardPINRequest, opts ...grpc.CallOption) (*CardActionsTypeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CardActionsTypeResponse)
	err := c.cc.Invoke(ctx, CardAdministrationService_SetCardPIN_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cardAdministrationServiceClient) ChargeCardAccountBalance(ctx context.Context, in *ChargeCardAccountBalanceRequest, opts ...grpc.CallOption) (*ChargeAccountBalanceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ChargeAccountBalanceResponse)
	err := c.cc.Invoke(ctx, CardAdministrationService_ChargeCardAccountBalance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CardAdministrationServiceServer is the server API for CardAdministrationService service.
// All implementations must embed UnimplementedCardAdministrationServiceServer
// for forward compatibility.
//
// Card Administration Service API for managing user cards and related operations.
type CardAdministrationServiceServer interface {
	// Create a new issuer identifier
	CreateIssuerIdentifier(context.Context, *CreateIssuerIdentifierRequest) (*IssuerIdentifierActionsResponse, error)
	// Update an existing issuer identifier
	UpdateIssuerIdentifier(context.Context, *UpdateIssuerIdentifierRequest) (*IssuerIdentifierActionsResponse, error)
	CreateCardType(context.Context, *CreateCardTypeRequest) (*CardTypeActionsResponse, error)
	UpdateCardType(context.Context, *UpdateCardTypeRequest) (*CardTypeActionsResponse, error)
	// Create a new card for a user
	CreateCard(context.Context, *CreateCardRequest) (*CardActionsTypeResponse, error)
	SetCardStatus(context.Context, *SetCardStatusRequest) (*CardActionsTypeResponse, error)
	SetCardMetaData(context.Context, *SetCardMetaDataRequest) (*CardActionsTypeResponse, error)
	SetCardPIN(context.Context, *SetCardPINRequest) (*CardActionsTypeResponse, error)
	ChargeCardAccountBalance(context.Context, *ChargeCardAccountBalanceRequest) (*ChargeAccountBalanceResponse, error)
	mustEmbedUnimplementedCardAdministrationServiceServer()
}

// UnimplementedCardAdministrationServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedCardAdministrationServiceServer struct{}

func (UnimplementedCardAdministrationServiceServer) CreateIssuerIdentifier(context.Context, *CreateIssuerIdentifierRequest) (*IssuerIdentifierActionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateIssuerIdentifier not implemented")
}
func (UnimplementedCardAdministrationServiceServer) UpdateIssuerIdentifier(context.Context, *UpdateIssuerIdentifierRequest) (*IssuerIdentifierActionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateIssuerIdentifier not implemented")
}
func (UnimplementedCardAdministrationServiceServer) CreateCardType(context.Context, *CreateCardTypeRequest) (*CardTypeActionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCardType not implemented")
}
func (UnimplementedCardAdministrationServiceServer) UpdateCardType(context.Context, *UpdateCardTypeRequest) (*CardTypeActionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCardType not implemented")
}
func (UnimplementedCardAdministrationServiceServer) CreateCard(context.Context, *CreateCardRequest) (*CardActionsTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCard not implemented")
}
func (UnimplementedCardAdministrationServiceServer) SetCardStatus(context.Context, *SetCardStatusRequest) (*CardActionsTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetCardStatus not implemented")
}
func (UnimplementedCardAdministrationServiceServer) SetCardMetaData(context.Context, *SetCardMetaDataRequest) (*CardActionsTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetCardMetaData not implemented")
}
func (UnimplementedCardAdministrationServiceServer) SetCardPIN(context.Context, *SetCardPINRequest) (*CardActionsTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetCardPIN not implemented")
}
func (UnimplementedCardAdministrationServiceServer) ChargeCardAccountBalance(context.Context, *ChargeCardAccountBalanceRequest) (*ChargeAccountBalanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChargeCardAccountBalance not implemented")
}
func (UnimplementedCardAdministrationServiceServer) mustEmbedUnimplementedCardAdministrationServiceServer() {
}
func (UnimplementedCardAdministrationServiceServer) testEmbeddedByValue() {}

// UnsafeCardAdministrationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CardAdministrationServiceServer will
// result in compilation errors.
type UnsafeCardAdministrationServiceServer interface {
	mustEmbedUnimplementedCardAdministrationServiceServer()
}

func RegisterCardAdministrationServiceServer(s grpc.ServiceRegistrar, srv CardAdministrationServiceServer) {
	// If the following call pancis, it indicates UnimplementedCardAdministrationServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&CardAdministrationService_ServiceDesc, srv)
}

func _CardAdministrationService_CreateIssuerIdentifier_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateIssuerIdentifierRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardAdministrationServiceServer).CreateIssuerIdentifier(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardAdministrationService_CreateIssuerIdentifier_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardAdministrationServiceServer).CreateIssuerIdentifier(ctx, req.(*CreateIssuerIdentifierRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardAdministrationService_UpdateIssuerIdentifier_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateIssuerIdentifierRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardAdministrationServiceServer).UpdateIssuerIdentifier(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardAdministrationService_UpdateIssuerIdentifier_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardAdministrationServiceServer).UpdateIssuerIdentifier(ctx, req.(*UpdateIssuerIdentifierRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardAdministrationService_CreateCardType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCardTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardAdministrationServiceServer).CreateCardType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardAdministrationService_CreateCardType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardAdministrationServiceServer).CreateCardType(ctx, req.(*CreateCardTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardAdministrationService_UpdateCardType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCardTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardAdministrationServiceServer).UpdateCardType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardAdministrationService_UpdateCardType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardAdministrationServiceServer).UpdateCardType(ctx, req.(*UpdateCardTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardAdministrationService_CreateCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardAdministrationServiceServer).CreateCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardAdministrationService_CreateCard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardAdministrationServiceServer).CreateCard(ctx, req.(*CreateCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardAdministrationService_SetCardStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetCardStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardAdministrationServiceServer).SetCardStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardAdministrationService_SetCardStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardAdministrationServiceServer).SetCardStatus(ctx, req.(*SetCardStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardAdministrationService_SetCardMetaData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetCardMetaDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardAdministrationServiceServer).SetCardMetaData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardAdministrationService_SetCardMetaData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardAdministrationServiceServer).SetCardMetaData(ctx, req.(*SetCardMetaDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardAdministrationService_SetCardPIN_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetCardPINRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardAdministrationServiceServer).SetCardPIN(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardAdministrationService_SetCardPIN_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardAdministrationServiceServer).SetCardPIN(ctx, req.(*SetCardPINRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CardAdministrationService_ChargeCardAccountBalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChargeCardAccountBalanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CardAdministrationServiceServer).ChargeCardAccountBalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CardAdministrationService_ChargeCardAccountBalance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CardAdministrationServiceServer).ChargeCardAccountBalance(ctx, req.(*ChargeCardAccountBalanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CardAdministrationService_ServiceDesc is the grpc.ServiceDesc for CardAdministrationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CardAdministrationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.CardAdministrationService",
	HandlerType: (*CardAdministrationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateIssuerIdentifier",
			Handler:    _CardAdministrationService_CreateIssuerIdentifier_Handler,
		},
		{
			MethodName: "UpdateIssuerIdentifier",
			Handler:    _CardAdministrationService_UpdateIssuerIdentifier_Handler,
		},
		{
			MethodName: "CreateCardType",
			Handler:    _CardAdministrationService_CreateCardType_Handler,
		},
		{
			MethodName: "UpdateCardType",
			Handler:    _CardAdministrationService_UpdateCardType_Handler,
		},
		{
			MethodName: "CreateCard",
			Handler:    _CardAdministrationService_CreateCard_Handler,
		},
		{
			MethodName: "SetCardStatus",
			Handler:    _CardAdministrationService_SetCardStatus_Handler,
		},
		{
			MethodName: "SetCardMetaData",
			Handler:    _CardAdministrationService_SetCardMetaData_Handler,
		},
		{
			MethodName: "SetCardPIN",
			Handler:    _CardAdministrationService_SetCardPIN_Handler,
		},
		{
			MethodName: "ChargeCardAccountBalance",
			Handler:    _CardAdministrationService_ChargeCardAccountBalance_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rpc_card_admin_service.proto",
}
