// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rpc_dto.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Response for getting context user cards list
type ContextUserCardsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// whether the request returns error or no.
	Error bool `protobuf:"varint,1,opt,name=error,proto3" json:"error,omitempty"`
	// error message if occurred or success message
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// list of user cards
	Cards         []*CardPAN `protobuf:"bytes,3,rep,name=cards,proto3" json:"cards,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ContextUserCardsResponse) Reset() {
	*x = ContextUserCardsResponse{}
	mi := &file_rpc_dto_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContextUserCardsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContextUserCardsResponse) ProtoMessage() {}

func (x *ContextUserCardsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContextUserCardsResponse.ProtoReflect.Descriptor instead.
func (*ContextUserCardsResponse) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{0}
}

func (x *ContextUserCardsResponse) GetError() bool {
	if x != nil {
		return x.Error
	}
	return false
}

func (x *ContextUserCardsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ContextUserCardsResponse) GetCards() []*CardPAN {
	if x != nil {
		return x.Cards
	}
	return nil
}

// Request for validating a card
type ValidateCardRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// card number to validate
	CardNumber string `protobuf:"bytes,1,opt,name=card_number,json=cardNumber,proto3" json:"card_number,omitempty"`
	// card holder name to validate
	Cvv string `protobuf:"bytes,2,opt,name=cvv,proto3" json:"cvv,omitempty"`
	// card expiration month to validate
	ExpireMonth string `protobuf:"bytes,3,opt,name=expire_month,json=expireMonth,proto3" json:"expire_month,omitempty"`
	// card expiration year to validate
	ExpireYear    string `protobuf:"bytes,4,opt,name=expire_year,json=expireYear,proto3" json:"expire_year,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidateCardRequest) Reset() {
	*x = ValidateCardRequest{}
	mi := &file_rpc_dto_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidateCardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateCardRequest) ProtoMessage() {}

func (x *ValidateCardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateCardRequest.ProtoReflect.Descriptor instead.
func (*ValidateCardRequest) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{1}
}

func (x *ValidateCardRequest) GetCardNumber() string {
	if x != nil {
		return x.CardNumber
	}
	return ""
}

func (x *ValidateCardRequest) GetCvv() string {
	if x != nil {
		return x.Cvv
	}
	return ""
}

func (x *ValidateCardRequest) GetExpireMonth() string {
	if x != nil {
		return x.ExpireMonth
	}
	return ""
}

func (x *ValidateCardRequest) GetExpireYear() string {
	if x != nil {
		return x.ExpireYear
	}
	return ""
}

// Response for validating a card
type ValidateCardResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// whether the request returns error or no.
	Error bool `protobuf:"varint,1,opt,name=error,proto3" json:"error,omitempty"`
	// error message if occurred or success message
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// whether the card is valid or not
	IsValid bool `protobuf:"varint,3,opt,name=is_valid,json=isValid,proto3" json:"is_valid,omitempty"`
	// card details if the card is valid
	Card          *CardPAN `protobuf:"bytes,4,opt,name=card,proto3" json:"card,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidateCardResponse) Reset() {
	*x = ValidateCardResponse{}
	mi := &file_rpc_dto_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidateCardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateCardResponse) ProtoMessage() {}

func (x *ValidateCardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateCardResponse.ProtoReflect.Descriptor instead.
func (*ValidateCardResponse) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{2}
}

func (x *ValidateCardResponse) GetError() bool {
	if x != nil {
		return x.Error
	}
	return false
}

func (x *ValidateCardResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ValidateCardResponse) GetIsValid() bool {
	if x != nil {
		return x.IsValid
	}
	return false
}

func (x *ValidateCardResponse) GetCard() *CardPAN {
	if x != nil {
		return x.Card
	}
	return nil
}

// Define an empty message for methods that require no input.
type Empty struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Empty) Reset() {
	*x = Empty{}
	mi := &file_rpc_dto_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{3}
}

var File_rpc_dto_proto protoreflect.FileDescriptor

const file_rpc_dto_proto_rawDesc = "" +
	"\n" +
	"\rrpc_dto.proto\x12\x02pb\x1a\x0erpc_card.proto\"m\n" +
	"\x18ContextUserCardsResponse\x12\x14\n" +
	"\x05error\x18\x01 \x01(\bR\x05error\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12!\n" +
	"\x05cards\x18\x03 \x03(\v2\v.pb.CardPANR\x05cards\"\x8c\x01\n" +
	"\x13ValidateCardRequest\x12\x1f\n" +
	"\vcard_number\x18\x01 \x01(\tR\n" +
	"cardNumber\x12\x10\n" +
	"\x03cvv\x18\x02 \x01(\tR\x03cvv\x12!\n" +
	"\fexpire_month\x18\x03 \x01(\tR\vexpireMonth\x12\x1f\n" +
	"\vexpire_year\x18\x04 \x01(\tR\n" +
	"expireYear\"\x82\x01\n" +
	"\x14ValidateCardResponse\x12\x14\n" +
	"\x05error\x18\x01 \x01(\bR\x05error\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x19\n" +
	"\bis_valid\x18\x03 \x01(\bR\aisValid\x12\x1f\n" +
	"\x04card\x18\x04 \x01(\v2\v.pb.CardPANR\x04card\"\a\n" +
	"\x05EmptyB%Z#github.com/liveutil/card_service/pbb\x06proto3"

var (
	file_rpc_dto_proto_rawDescOnce sync.Once
	file_rpc_dto_proto_rawDescData []byte
)

func file_rpc_dto_proto_rawDescGZIP() []byte {
	file_rpc_dto_proto_rawDescOnce.Do(func() {
		file_rpc_dto_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rpc_dto_proto_rawDesc), len(file_rpc_dto_proto_rawDesc)))
	})
	return file_rpc_dto_proto_rawDescData
}

var file_rpc_dto_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_rpc_dto_proto_goTypes = []any{
	(*ContextUserCardsResponse)(nil), // 0: pb.ContextUserCardsResponse
	(*ValidateCardRequest)(nil),      // 1: pb.ValidateCardRequest
	(*ValidateCardResponse)(nil),     // 2: pb.ValidateCardResponse
	(*Empty)(nil),                    // 3: pb.Empty
	(*CardPAN)(nil),                  // 4: pb.CardPAN
}
var file_rpc_dto_proto_depIdxs = []int32{
	4, // 0: pb.ContextUserCardsResponse.cards:type_name -> pb.CardPAN
	4, // 1: pb.ValidateCardResponse.card:type_name -> pb.CardPAN
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_rpc_dto_proto_init() }
func file_rpc_dto_proto_init() {
	if File_rpc_dto_proto != nil {
		return
	}
	file_rpc_card_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_dto_proto_rawDesc), len(file_rpc_dto_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_rpc_dto_proto_goTypes,
		DependencyIndexes: file_rpc_dto_proto_depIdxs,
		MessageInfos:      file_rpc_dto_proto_msgTypes,
	}.Build()
	File_rpc_dto_proto = out.File
	file_rpc_dto_proto_goTypes = nil
	file_rpc_dto_proto_depIdxs = nil
}
