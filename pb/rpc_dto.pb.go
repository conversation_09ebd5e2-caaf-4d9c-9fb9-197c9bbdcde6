// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rpc_dto.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ContextUserOrdersRequest
type ContextUserOrdersRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// page size
	PageSize int32 `protobuf:"varint,1,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// page offset
	PageOffset    int32 `protobuf:"varint,2,opt,name=page_offset,json=pageOffset,proto3" json:"page_offset,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ContextUserOrdersRequest) Reset() {
	*x = ContextUserOrdersRequest{}
	mi := &file_rpc_dto_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContextUserOrdersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContextUserOrdersRequest) ProtoMessage() {}

func (x *ContextUserOrdersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContextUserOrdersRequest.ProtoReflect.Descriptor instead.
func (*ContextUserOrdersRequest) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{0}
}

func (x *ContextUserOrdersRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ContextUserOrdersRequest) GetPageOffset() int32 {
	if x != nil {
		return x.PageOffset
	}
	return 0
}

// Response for getting context user orders list
type ContextUserOrdersResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// whether the request returns error or no.
	Error bool `protobuf:"varint,1,opt,name=error,proto3" json:"error,omitempty"`
	// error message if occurred or success message
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// result array length
	ResultLength int32 `protobuf:"varint,3,opt,name=result_length,json=resultLength,proto3" json:"result_length,omitempty"`
	// list of user orders
	Orders        []*Order `protobuf:"bytes,4,rep,name=orders,proto3" json:"orders,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ContextUserOrdersResponse) Reset() {
	*x = ContextUserOrdersResponse{}
	mi := &file_rpc_dto_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContextUserOrdersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContextUserOrdersResponse) ProtoMessage() {}

func (x *ContextUserOrdersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContextUserOrdersResponse.ProtoReflect.Descriptor instead.
func (*ContextUserOrdersResponse) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{1}
}

func (x *ContextUserOrdersResponse) GetError() bool {
	if x != nil {
		return x.Error
	}
	return false
}

func (x *ContextUserOrdersResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ContextUserOrdersResponse) GetResultLength() int32 {
	if x != nil {
		return x.ResultLength
	}
	return 0
}

func (x *ContextUserOrdersResponse) GetOrders() []*Order {
	if x != nil {
		return x.Orders
	}
	return nil
}

// Request for placing an order
type PlaceOrderRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// card pan to be charged
	CardPan string `protobuf:"bytes,1,opt,name=card_pan,json=cardPan,proto3" json:"card_pan,omitempty"`
	// amount to be charged
	Amount string `protobuf:"bytes,2,opt,name=amount,proto3" json:"amount,omitempty"`
	// asset to be charged
	Asset string `protobuf:"bytes,3,opt,name=asset,proto3" json:"asset,omitempty"`
	// reference for idempotency
	Reference string `protobuf:"bytes,4,opt,name=reference,proto3" json:"reference,omitempty"`
	// description for order
	Description string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	// target domain for order
	TargetDomain string `protobuf:"bytes,6,opt,name=target_domain,json=targetDomain,proto3" json:"target_domain,omitempty"`
	// voucher code to be applied
	VoucherCode   string `protobuf:"bytes,7,opt,name=voucher_code,json=voucherCode,proto3" json:"voucher_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlaceOrderRequest) Reset() {
	*x = PlaceOrderRequest{}
	mi := &file_rpc_dto_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlaceOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaceOrderRequest) ProtoMessage() {}

func (x *PlaceOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaceOrderRequest.ProtoReflect.Descriptor instead.
func (*PlaceOrderRequest) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{2}
}

func (x *PlaceOrderRequest) GetCardPan() string {
	if x != nil {
		return x.CardPan
	}
	return ""
}

func (x *PlaceOrderRequest) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *PlaceOrderRequest) GetAsset() string {
	if x != nil {
		return x.Asset
	}
	return ""
}

func (x *PlaceOrderRequest) GetReference() string {
	if x != nil {
		return x.Reference
	}
	return ""
}

func (x *PlaceOrderRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PlaceOrderRequest) GetTargetDomain() string {
	if x != nil {
		return x.TargetDomain
	}
	return ""
}

func (x *PlaceOrderRequest) GetVoucherCode() string {
	if x != nil {
		return x.VoucherCode
	}
	return ""
}

// Response for placing an order
type OrderActionsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// whether the request returns error or no.
	Error bool `protobuf:"varint,1,opt,name=error,proto3" json:"error,omitempty"`
	// error message if occurred or success message
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// created order
	Order         *Order `protobuf:"bytes,3,opt,name=order,proto3" json:"order,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OrderActionsResponse) Reset() {
	*x = OrderActionsResponse{}
	mi := &file_rpc_dto_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrderActionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderActionsResponse) ProtoMessage() {}

func (x *OrderActionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderActionsResponse.ProtoReflect.Descriptor instead.
func (*OrderActionsResponse) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{3}
}

func (x *OrderActionsResponse) GetError() bool {
	if x != nil {
		return x.Error
	}
	return false
}

func (x *OrderActionsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *OrderActionsResponse) GetOrder() *Order {
	if x != nil {
		return x.Order
	}
	return nil
}

// PayOrderRequest
type PayOrderRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// order reference
	Reference string `protobuf:"bytes,1,opt,name=reference,proto3" json:"reference,omitempty"`
	// gateway identifier
	Gateway       string `protobuf:"bytes,2,opt,name=gateway,proto3" json:"gateway,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PayOrderRequest) Reset() {
	*x = PayOrderRequest{}
	mi := &file_rpc_dto_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PayOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayOrderRequest) ProtoMessage() {}

func (x *PayOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayOrderRequest.ProtoReflect.Descriptor instead.
func (*PayOrderRequest) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{4}
}

func (x *PayOrderRequest) GetReference() string {
	if x != nil {
		return x.Reference
	}
	return ""
}

func (x *PayOrderRequest) GetGateway() string {
	if x != nil {
		return x.Gateway
	}
	return ""
}

// PayOrderResponse
type PayOrderResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// whether the request returns error or no.
	Error bool `protobuf:"varint,1,opt,name=error,proto3" json:"error,omitempty"`
	// error message if occurred or success message
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// generated payment gateway url
	Url           string `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PayOrderResponse) Reset() {
	*x = PayOrderResponse{}
	mi := &file_rpc_dto_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PayOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayOrderResponse) ProtoMessage() {}

func (x *PayOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayOrderResponse.ProtoReflect.Descriptor instead.
func (*PayOrderResponse) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{5}
}

func (x *PayOrderResponse) GetError() bool {
	if x != nil {
		return x.Error
	}
	return false
}

func (x *PayOrderResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *PayOrderResponse) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

// OrderAction Request
type OrderActionRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// reference for order
	Reference     string `protobuf:"bytes,1,opt,name=reference,proto3" json:"reference,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OrderActionRequest) Reset() {
	*x = OrderActionRequest{}
	mi := &file_rpc_dto_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrderActionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderActionRequest) ProtoMessage() {}

func (x *OrderActionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderActionRequest.ProtoReflect.Descriptor instead.
func (*OrderActionRequest) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{6}
}

func (x *OrderActionRequest) GetReference() string {
	if x != nil {
		return x.Reference
	}
	return ""
}

// Define an empty message for methods that require no input.
type Empty struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Empty) Reset() {
	*x = Empty{}
	mi := &file_rpc_dto_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_dto_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_rpc_dto_proto_rawDescGZIP(), []int{7}
}

var File_rpc_dto_proto protoreflect.FileDescriptor

const file_rpc_dto_proto_rawDesc = "" +
	"\n" +
	"\rrpc_dto.proto\x12\x02pb\x1a\x13rpc_order_dto.proto\"X\n" +
	"\x18ContextUserOrdersRequest\x12\x1b\n" +
	"\tpage_size\x18\x01 \x01(\x05R\bpageSize\x12\x1f\n" +
	"\vpage_offset\x18\x02 \x01(\x05R\n" +
	"pageOffset\"\x93\x01\n" +
	"\x19ContextUserOrdersResponse\x12\x14\n" +
	"\x05error\x18\x01 \x01(\bR\x05error\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12#\n" +
	"\rresult_length\x18\x03 \x01(\x05R\fresultLength\x12!\n" +
	"\x06orders\x18\x04 \x03(\v2\t.pb.OrderR\x06orders\"\xe4\x01\n" +
	"\x11PlaceOrderRequest\x12\x19\n" +
	"\bcard_pan\x18\x01 \x01(\tR\acardPan\x12\x16\n" +
	"\x06amount\x18\x02 \x01(\tR\x06amount\x12\x14\n" +
	"\x05asset\x18\x03 \x01(\tR\x05asset\x12\x1c\n" +
	"\treference\x18\x04 \x01(\tR\treference\x12 \n" +
	"\vdescription\x18\x05 \x01(\tR\vdescription\x12#\n" +
	"\rtarget_domain\x18\x06 \x01(\tR\ftargetDomain\x12!\n" +
	"\fvoucher_code\x18\a \x01(\tR\vvoucherCode\"g\n" +
	"\x14OrderActionsResponse\x12\x14\n" +
	"\x05error\x18\x01 \x01(\bR\x05error\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x1f\n" +
	"\x05order\x18\x03 \x01(\v2\t.pb.OrderR\x05order\"I\n" +
	"\x0fPayOrderRequest\x12\x1c\n" +
	"\treference\x18\x01 \x01(\tR\treference\x12\x18\n" +
	"\agateway\x18\x02 \x01(\tR\agateway\"T\n" +
	"\x10PayOrderResponse\x12\x14\n" +
	"\x05error\x18\x01 \x01(\bR\x05error\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x10\n" +
	"\x03url\x18\x03 \x01(\tR\x03url\"2\n" +
	"\x12OrderActionRequest\x12\x1c\n" +
	"\treference\x18\x01 \x01(\tR\treference\"\a\n" +
	"\x05EmptyB&Z$github.com/liveutil/order_service/pbb\x06proto3"

var (
	file_rpc_dto_proto_rawDescOnce sync.Once
	file_rpc_dto_proto_rawDescData []byte
)

func file_rpc_dto_proto_rawDescGZIP() []byte {
	file_rpc_dto_proto_rawDescOnce.Do(func() {
		file_rpc_dto_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rpc_dto_proto_rawDesc), len(file_rpc_dto_proto_rawDesc)))
	})
	return file_rpc_dto_proto_rawDescData
}

var file_rpc_dto_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_rpc_dto_proto_goTypes = []any{
	(*ContextUserOrdersRequest)(nil),  // 0: pb.ContextUserOrdersRequest
	(*ContextUserOrdersResponse)(nil), // 1: pb.ContextUserOrdersResponse
	(*PlaceOrderRequest)(nil),         // 2: pb.PlaceOrderRequest
	(*OrderActionsResponse)(nil),      // 3: pb.OrderActionsResponse
	(*PayOrderRequest)(nil),           // 4: pb.PayOrderRequest
	(*PayOrderResponse)(nil),          // 5: pb.PayOrderResponse
	(*OrderActionRequest)(nil),        // 6: pb.OrderActionRequest
	(*Empty)(nil),                     // 7: pb.Empty
	(*Order)(nil),                     // 8: pb.Order
}
var file_rpc_dto_proto_depIdxs = []int32{
	8, // 0: pb.ContextUserOrdersResponse.orders:type_name -> pb.Order
	8, // 1: pb.OrderActionsResponse.order:type_name -> pb.Order
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_rpc_dto_proto_init() }
func file_rpc_dto_proto_init() {
	if File_rpc_dto_proto != nil {
		return
	}
	file_rpc_order_dto_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_dto_proto_rawDesc), len(file_rpc_dto_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_rpc_dto_proto_goTypes,
		DependencyIndexes: file_rpc_dto_proto_depIdxs,
		MessageInfos:      file_rpc_dto_proto_msgTypes,
	}.Build()
	File_rpc_dto_proto = out.File
	file_rpc_dto_proto_goTypes = nil
	file_rpc_dto_proto_depIdxs = nil
}
