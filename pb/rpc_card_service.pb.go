// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rpc_card_service.proto

package pb

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_rpc_card_service_proto protoreflect.FileDescriptor

const file_rpc_card_service_proto_rawDesc = "" +
	"\n" +
	"\x16rpc_card_service.proto\x12\x02pb\x1a\x1cgoogle/api/annotations.proto\x1a.protoc-gen-openapiv2/options/annotations.proto\x1a\rrpc_dto.proto2\xd4\x01\n" +
	"\vCardService\x12_\n" +
	"\x10ContextUserCards\x12\t.pb.Empty\x1a\x1c.pb.ContextUserCardsResponse\"\"\x82\xd3\xe4\x93\x02\x1c\x12\x1a/v1/card/context_get_cards\x12d\n" +
	"\fValidateCard\x12\x17.pb.ValidateCardRequest\x1a\x18.pb.ValidateCardResponse\"!\x82\xd3\xe4\x93\x02\x1b:\x01*\"\x16/v1/card/validate_cardB\xc9\x02\x92A\xa0\x02\x12\x8c\x01\n" +
	"\x10Card Service API\x12@Card Service API for managing user cards and related operations.\"/\n" +
	"\x18Card Service API Support\x1a\x13liveutil@icloud.com2\x051.0.0*\x02\x01\x022\x10application/json:\x10application/jsonZY\n" +
	"W\n" +
	"\x06Bearer\x12M\b\x02\x128Authentication token, prefixed by Bearer: Bearer <token>\x1a\rAuthorization \x02b\f\n" +
	"\n" +
	"\n" +
	"\x06Bearer\x12\x00Z#github.com/liveutil/card_service/pbb\x06proto3"

var file_rpc_card_service_proto_goTypes = []any{
	(*Empty)(nil),                    // 0: pb.Empty
	(*ValidateCardRequest)(nil),      // 1: pb.ValidateCardRequest
	(*ContextUserCardsResponse)(nil), // 2: pb.ContextUserCardsResponse
	(*ValidateCardResponse)(nil),     // 3: pb.ValidateCardResponse
}
var file_rpc_card_service_proto_depIdxs = []int32{
	0, // 0: pb.CardService.ContextUserCards:input_type -> pb.Empty
	1, // 1: pb.CardService.ValidateCard:input_type -> pb.ValidateCardRequest
	2, // 2: pb.CardService.ContextUserCards:output_type -> pb.ContextUserCardsResponse
	3, // 3: pb.CardService.ValidateCard:output_type -> pb.ValidateCardResponse
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_rpc_card_service_proto_init() }
func file_rpc_card_service_proto_init() {
	if File_rpc_card_service_proto != nil {
		return
	}
	file_rpc_dto_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_card_service_proto_rawDesc), len(file_rpc_card_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_rpc_card_service_proto_goTypes,
		DependencyIndexes: file_rpc_card_service_proto_depIdxs,
	}.Build()
	File_rpc_card_service_proto = out.File
	file_rpc_card_service_proto_goTypes = nil
	file_rpc_card_service_proto_depIdxs = nil
}
