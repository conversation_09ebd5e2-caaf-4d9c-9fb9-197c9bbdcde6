// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rpc_health.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Request for checking health status of the service and required services
type HealthCheckRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// service name to check health status for
	Service       string `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckRequest) Reset() {
	*x = HealthCheckRequest{}
	mi := &file_rpc_health_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckRequest) ProtoMessage() {}

func (x *HealthCheckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_health_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckRequest.ProtoReflect.Descriptor instead.
func (*HealthCheckRequest) Descriptor() ([]byte, []int) {
	return file_rpc_health_proto_rawDescGZIP(), []int{0}
}

func (x *HealthCheckRequest) GetService() string {
	if x != nil {
		return x.Service
	}
	return ""
}

// Response for checking health status of the service and required services
type HealthCheckResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// health status of the service and required services
	Status string `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// timestamp of the health check
	Timestamp int64 `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	// details of the health status of the service and required services
	Details       *Details `protobuf:"bytes,3,opt,name=details,proto3" json:"details,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckResponse) Reset() {
	*x = HealthCheckResponse{}
	mi := &file_rpc_health_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckResponse) ProtoMessage() {}

func (x *HealthCheckResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_health_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckResponse.ProtoReflect.Descriptor instead.
func (*HealthCheckResponse) Descriptor() ([]byte, []int) {
	return file_rpc_health_proto_rawDescGZIP(), []int{1}
}

func (x *HealthCheckResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *HealthCheckResponse) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *HealthCheckResponse) GetDetails() *Details {
	if x != nil {
		return x.Details
	}
	return nil
}

// Details of the health status of the service and required services
type Details struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// health status of the database
	Database string `protobuf:"bytes,1,opt,name=database,proto3" json:"database,omitempty"`
	// health status of the redis
	Redis string `protobuf:"bytes,2,opt,name=redis,proto3" json:"redis,omitempty"`
	// health status of the mongodb
	Mongodb string `protobuf:"bytes,3,opt,name=mongodb,proto3" json:"mongodb,omitempty"`
	// health status of the message bus (NATS)
	MessageBus string `protobuf:"bytes,4,opt,name=message_bus,json=messageBus,proto3" json:"message_bus,omitempty"`
	// health status of the service mesh (Dapr) or (Builtin NATS based service mesh client)
	ServiceMesh   string `protobuf:"bytes,5,opt,name=service_mesh,json=serviceMesh,proto3" json:"service_mesh,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Details) Reset() {
	*x = Details{}
	mi := &file_rpc_health_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Details) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Details) ProtoMessage() {}

func (x *Details) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_health_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Details.ProtoReflect.Descriptor instead.
func (*Details) Descriptor() ([]byte, []int) {
	return file_rpc_health_proto_rawDescGZIP(), []int{2}
}

func (x *Details) GetDatabase() string {
	if x != nil {
		return x.Database
	}
	return ""
}

func (x *Details) GetRedis() string {
	if x != nil {
		return x.Redis
	}
	return ""
}

func (x *Details) GetMongodb() string {
	if x != nil {
		return x.Mongodb
	}
	return ""
}

func (x *Details) GetMessageBus() string {
	if x != nil {
		return x.MessageBus
	}
	return ""
}

func (x *Details) GetServiceMesh() string {
	if x != nil {
		return x.ServiceMesh
	}
	return ""
}

var File_rpc_health_proto protoreflect.FileDescriptor

const file_rpc_health_proto_rawDesc = "" +
	"\n" +
	"\x10rpc_health.proto\x12\x02pb\".\n" +
	"\x12HealthCheckRequest\x12\x18\n" +
	"\aservice\x18\x01 \x01(\tR\aservice\"r\n" +
	"\x13HealthCheckResponse\x12\x16\n" +
	"\x06status\x18\x01 \x01(\tR\x06status\x12\x1c\n" +
	"\ttimestamp\x18\x02 \x01(\x03R\ttimestamp\x12%\n" +
	"\adetails\x18\x03 \x01(\v2\v.pb.DetailsR\adetails\"\x99\x01\n" +
	"\aDetails\x12\x1a\n" +
	"\bdatabase\x18\x01 \x01(\tR\bdatabase\x12\x14\n" +
	"\x05redis\x18\x02 \x01(\tR\x05redis\x12\x18\n" +
	"\amongodb\x18\x03 \x01(\tR\amongodb\x12\x1f\n" +
	"\vmessage_bus\x18\x04 \x01(\tR\n" +
	"messageBus\x12!\n" +
	"\fservice_mesh\x18\x05 \x01(\tR\vserviceMesh2\x82\x01\n" +
	"\x06Health\x12:\n" +
	"\x05Check\x12\x16.pb.HealthCheckRequest\x1a\x17.pb.HealthCheckResponse\"\x00\x12<\n" +
	"\x05Watch\x12\x16.pb.HealthCheckRequest\x1a\x17.pb.HealthCheckResponse\"\x000\x01B&Z$github.com/liveutil/order_service/pbb\x06proto3"

var (
	file_rpc_health_proto_rawDescOnce sync.Once
	file_rpc_health_proto_rawDescData []byte
)

func file_rpc_health_proto_rawDescGZIP() []byte {
	file_rpc_health_proto_rawDescOnce.Do(func() {
		file_rpc_health_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rpc_health_proto_rawDesc), len(file_rpc_health_proto_rawDesc)))
	})
	return file_rpc_health_proto_rawDescData
}

var file_rpc_health_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_rpc_health_proto_goTypes = []any{
	(*HealthCheckRequest)(nil),  // 0: pb.HealthCheckRequest
	(*HealthCheckResponse)(nil), // 1: pb.HealthCheckResponse
	(*Details)(nil),             // 2: pb.Details
}
var file_rpc_health_proto_depIdxs = []int32{
	2, // 0: pb.HealthCheckResponse.details:type_name -> pb.Details
	0, // 1: pb.Health.Check:input_type -> pb.HealthCheckRequest
	0, // 2: pb.Health.Watch:input_type -> pb.HealthCheckRequest
	1, // 3: pb.Health.Check:output_type -> pb.HealthCheckResponse
	1, // 4: pb.Health.Watch:output_type -> pb.HealthCheckResponse
	3, // [3:5] is the sub-list for method output_type
	1, // [1:3] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_rpc_health_proto_init() }
func file_rpc_health_proto_init() {
	if File_rpc_health_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_health_proto_rawDesc), len(file_rpc_health_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_rpc_health_proto_goTypes,
		DependencyIndexes: file_rpc_health_proto_depIdxs,
		MessageInfos:      file_rpc_health_proto_msgTypes,
	}.Build()
	File_rpc_health_proto = out.File
	file_rpc_health_proto_goTypes = nil
	file_rpc_health_proto_depIdxs = nil
}
