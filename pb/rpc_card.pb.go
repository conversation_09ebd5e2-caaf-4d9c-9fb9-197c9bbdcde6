// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rpc_card.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// issuer_identifier contains 'System Master Data' for IIN (Issuer Identification Number) registry
// It stores information about card issuers, including their unique identifiers, names, logos, and URLs.
// The model is designed to manage the metadata associated with each issuer, allowing for easy retrieval and updates.
// The model includes fields for the issuer's IIN, external identifier, name, logo URL, website URL, and additional metadata.
// It also includes timestamps for creation, updates, and deletion, enabling tracking of the lifecycle of each issuer record.
// The model is indexed on various fields to optimize query performance and ensure efficient data retrieval.
// The model structure is as follows:
type IssuerIdentifier struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Id    int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// unique external identifier for internal-external system identifier isolation
	Identifier string `protobuf:"bytes,2,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// iin number
	Iin string `protobuf:"bytes,3,opt,name=iin,proto3" json:"iin,omitempty"`
	// iin status
	Status string `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	// issuer owner user identifier
	IssuerIdentifier string `protobuf:"bytes,5,opt,name=issuer_identifier,json=issuerIdentifier,proto3" json:"issuer_identifier,omitempty"`
	// issuer name
	IssuerName string `protobuf:"bytes,6,opt,name=issuer_name,json=issuerName,proto3" json:"issuer_name,omitempty"`
	// issuer logo url
	IssuerLogo string `protobuf:"bytes,7,opt,name=issuer_logo,json=issuerLogo,proto3" json:"issuer_logo,omitempty"`
	// issuer website url
	IssuerUrl string `protobuf:"bytes,8,opt,name=issuer_url,json=issuerUrl,proto3" json:"issuer_url,omitempty"`
	// metadata
	MetaData string `protobuf:"bytes,9,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`
	// expire date
	ExpireDate *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=expire_date,json=expireDate,proto3" json:"expire_date,omitempty"`
	// creation time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// update time
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// deletion time
	DeletedAt     *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IssuerIdentifier) Reset() {
	*x = IssuerIdentifier{}
	mi := &file_rpc_card_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IssuerIdentifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IssuerIdentifier) ProtoMessage() {}

func (x *IssuerIdentifier) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_card_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IssuerIdentifier.ProtoReflect.Descriptor instead.
func (*IssuerIdentifier) Descriptor() ([]byte, []int) {
	return file_rpc_card_proto_rawDescGZIP(), []int{0}
}

func (x *IssuerIdentifier) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *IssuerIdentifier) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *IssuerIdentifier) GetIin() string {
	if x != nil {
		return x.Iin
	}
	return ""
}

func (x *IssuerIdentifier) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *IssuerIdentifier) GetIssuerIdentifier() string {
	if x != nil {
		return x.IssuerIdentifier
	}
	return ""
}

func (x *IssuerIdentifier) GetIssuerName() string {
	if x != nil {
		return x.IssuerName
	}
	return ""
}

func (x *IssuerIdentifier) GetIssuerLogo() string {
	if x != nil {
		return x.IssuerLogo
	}
	return ""
}

func (x *IssuerIdentifier) GetIssuerUrl() string {
	if x != nil {
		return x.IssuerUrl
	}
	return ""
}

func (x *IssuerIdentifier) GetMetaData() string {
	if x != nil {
		return x.MetaData
	}
	return ""
}

func (x *IssuerIdentifier) GetExpireDate() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpireDate
	}
	return nil
}

func (x *IssuerIdentifier) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *IssuerIdentifier) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *IssuerIdentifier) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

// CardType model contains 'System Master Data' for card types
// It stores information about different card types, including their unique identifiers, codes, names, descriptions, and metadata.
// The model is designed to manage the metadata associated with each card type, allowing for easy retrieval and updates.
// The model includes fields for the card type's code, name, description, and additional metadata.
// It also includes timestamps for creation, updates, and deletion, enabling tracking of the lifecycle of each card type record.
type CardType struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Id    int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// unique external identifier for internal-external system identifier isolation
	Identifier string `protobuf:"bytes,2,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// card type code
	Code string `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"`
	// card type name
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// card type description
	Description string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	// card type metadata
	MetaData string `protobuf:"bytes,6,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`
	// creation time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// update time
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// deletion time
	DeletedAt     *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CardType) Reset() {
	*x = CardType{}
	mi := &file_rpc_card_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CardType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardType) ProtoMessage() {}

func (x *CardType) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_card_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardType.ProtoReflect.Descriptor instead.
func (*CardType) Descriptor() ([]byte, []int) {
	return file_rpc_card_proto_rawDescGZIP(), []int{1}
}

func (x *CardType) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CardType) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *CardType) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *CardType) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CardType) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CardType) GetMetaData() string {
	if x != nil {
		return x.MetaData
	}
	return ""
}

func (x *CardType) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CardType) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *CardType) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

// Card model contains 'System Master Data' for cards
// It stores all the necessary information about each card, including its status, identifiers, and metadata.
// The model is designed to handle various card statuses and includes fields for card details such as IIN, account number, CVV, pin code, card number, expiration date, and metadata.
// The model also includes timestamps for creation, updates, and deletion, allowing for tracking the lifecycle of each card.
// The model is indexed on various fields to optimize query performance and ensure efficient data retrieval.
// The model structure is as follows: Card PAN details
type Card struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Id    int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// unique external identifier for internal-external system identifier isolation
	Identifier string `protobuf:"bytes,2,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// owner of card external identifier
	UserIdentifier string `protobuf:"bytes,3,opt,name=user_identifier,json=userIdentifier,proto3" json:"user_identifier,omitempty"`
	// card status determinable value
	Status string `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	// card issuer identifier number id
	Iin int64 `protobuf:"varint,5,opt,name=iin,proto3" json:"iin,omitempty"`
	// card type id
	CardType int64 `protobuf:"varint,6,opt,name=card_type,json=cardType,proto3" json:"card_type,omitempty"`
	// card issuer identifier code number
	IinCode string `protobuf:"bytes,7,opt,name=iin_code,json=iinCode,proto3" json:"iin_code,omitempty"`
	// card type code
	CardTypeCode string `protobuf:"bytes,8,opt,name=card_type_code,json=cardTypeCode,proto3" json:"card_type_code,omitempty"`
	// card account number
	AccountNumber string `protobuf:"bytes,9,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	// luhn digit
	LuhnDigit string `protobuf:"bytes,10,opt,name=luhn_digit,json=luhnDigit,proto3" json:"luhn_digit,omitempty"`
	// cvv
	Cvv string `protobuf:"bytes,11,opt,name=cvv,proto3" json:"cvv,omitempty"`
	// pin code
	PinCode string `protobuf:"bytes,12,opt,name=pin_code,json=pinCode,proto3" json:"pin_code,omitempty"`
	// card number: iin_code(4)+card_type(1)+account_number(10)+luhn_digit(1)
	CardNumber string `protobuf:"bytes,13,opt,name=card_number,json=cardNumber,proto3" json:"card_number,omitempty"`
	// expire year
	ExpireYear string `protobuf:"bytes,14,opt,name=expire_year,json=expireYear,proto3" json:"expire_year,omitempty"`
	// expire month
	ExpireMonth string `protobuf:"bytes,15,opt,name=expire_month,json=expireMonth,proto3" json:"expire_month,omitempty"`
	// meta data
	MetaData string `protobuf:"bytes,16,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`
	// expire date and time of card
	ExpiresAt *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
	// creation time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// update time
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,19,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// deletion time
	DeletedAt     *timestamppb.Timestamp `protobuf:"bytes,20,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Card) Reset() {
	*x = Card{}
	mi := &file_rpc_card_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Card) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Card) ProtoMessage() {}

func (x *Card) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_card_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Card.ProtoReflect.Descriptor instead.
func (*Card) Descriptor() ([]byte, []int) {
	return file_rpc_card_proto_rawDescGZIP(), []int{2}
}

func (x *Card) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Card) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *Card) GetUserIdentifier() string {
	if x != nil {
		return x.UserIdentifier
	}
	return ""
}

func (x *Card) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Card) GetIin() int64 {
	if x != nil {
		return x.Iin
	}
	return 0
}

func (x *Card) GetCardType() int64 {
	if x != nil {
		return x.CardType
	}
	return 0
}

func (x *Card) GetIinCode() string {
	if x != nil {
		return x.IinCode
	}
	return ""
}

func (x *Card) GetCardTypeCode() string {
	if x != nil {
		return x.CardTypeCode
	}
	return ""
}

func (x *Card) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *Card) GetLuhnDigit() string {
	if x != nil {
		return x.LuhnDigit
	}
	return ""
}

func (x *Card) GetCvv() string {
	if x != nil {
		return x.Cvv
	}
	return ""
}

func (x *Card) GetPinCode() string {
	if x != nil {
		return x.PinCode
	}
	return ""
}

func (x *Card) GetCardNumber() string {
	if x != nil {
		return x.CardNumber
	}
	return ""
}

func (x *Card) GetExpireYear() string {
	if x != nil {
		return x.ExpireYear
	}
	return ""
}

func (x *Card) GetExpireMonth() string {
	if x != nil {
		return x.ExpireMonth
	}
	return ""
}

func (x *Card) GetMetaData() string {
	if x != nil {
		return x.MetaData
	}
	return ""
}

func (x *Card) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

func (x *Card) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Card) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Card) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

type CardPAN struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// unique external identifier for internal-external system identifier isolation
	Identifier string `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// owner of card external identifier
	UserIdentifier string `protobuf:"bytes,2,opt,name=user_identifier,json=userIdentifier,proto3" json:"user_identifier,omitempty"`
	// holder name
	HolderName string `protobuf:"bytes,3,opt,name=holder_name,json=holderName,proto3" json:"holder_name,omitempty"`
	// card status determinable value
	Status string `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	// card number: iin_code+account_number+luhn_digit
	CardNumber string `protobuf:"bytes,5,opt,name=card_number,json=cardNumber,proto3" json:"card_number,omitempty"`
	// cvv
	Cvv string `protobuf:"bytes,6,opt,name=cvv,proto3" json:"cvv,omitempty"`
	// expire year
	ExpireYear string `protobuf:"bytes,7,opt,name=expire_year,json=expireYear,proto3" json:"expire_year,omitempty"`
	// expire month
	ExpireMonth string `protobuf:"bytes,8,opt,name=expire_month,json=expireMonth,proto3" json:"expire_month,omitempty"`
	// expire date and time of card
	ExpiresAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
	// creation time
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// update time
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// deletion time
	DeletedAt     *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CardPAN) Reset() {
	*x = CardPAN{}
	mi := &file_rpc_card_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CardPAN) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardPAN) ProtoMessage() {}

func (x *CardPAN) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_card_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardPAN.ProtoReflect.Descriptor instead.
func (*CardPAN) Descriptor() ([]byte, []int) {
	return file_rpc_card_proto_rawDescGZIP(), []int{3}
}

func (x *CardPAN) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *CardPAN) GetUserIdentifier() string {
	if x != nil {
		return x.UserIdentifier
	}
	return ""
}

func (x *CardPAN) GetHolderName() string {
	if x != nil {
		return x.HolderName
	}
	return ""
}

func (x *CardPAN) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *CardPAN) GetCardNumber() string {
	if x != nil {
		return x.CardNumber
	}
	return ""
}

func (x *CardPAN) GetCvv() string {
	if x != nil {
		return x.Cvv
	}
	return ""
}

func (x *CardPAN) GetExpireYear() string {
	if x != nil {
		return x.ExpireYear
	}
	return ""
}

func (x *CardPAN) GetExpireMonth() string {
	if x != nil {
		return x.ExpireMonth
	}
	return ""
}

func (x *CardPAN) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

func (x *CardPAN) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CardPAN) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *CardPAN) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

var File_rpc_card_proto protoreflect.FileDescriptor

const file_rpc_card_proto_rawDesc = "" +
	"\n" +
	"\x0erpc_card.proto\x12\x02pb\x1a\x1fgoogle/protobuf/timestamp.proto\"\x85\x04\n" +
	"\x10IssuerIdentifier\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1e\n" +
	"\n" +
	"identifier\x18\x02 \x01(\tR\n" +
	"identifier\x12\x10\n" +
	"\x03iin\x18\x03 \x01(\tR\x03iin\x12\x16\n" +
	"\x06status\x18\x04 \x01(\tR\x06status\x12+\n" +
	"\x11issuer_identifier\x18\x05 \x01(\tR\x10issuerIdentifier\x12\x1f\n" +
	"\vissuer_name\x18\x06 \x01(\tR\n" +
	"issuerName\x12\x1f\n" +
	"\vissuer_logo\x18\a \x01(\tR\n" +
	"issuerLogo\x12\x1d\n" +
	"\n" +
	"issuer_url\x18\b \x01(\tR\tissuerUrl\x12\x1b\n" +
	"\tmeta_data\x18\t \x01(\tR\bmetaData\x12;\n" +
	"\vexpire_date\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"expireDate\x129\n" +
	"\n" +
	"created_at\x18\v \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\f \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x129\n" +
	"\n" +
	"deleted_at\x18\r \x01(\v2\x1a.google.protobuf.TimestampR\tdeletedAt\"\xd2\x02\n" +
	"\bCardType\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1e\n" +
	"\n" +
	"identifier\x18\x02 \x01(\tR\n" +
	"identifier\x12\x12\n" +
	"\x04code\x18\x03 \x01(\tR\x04code\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x05 \x01(\tR\vdescription\x12\x1b\n" +
	"\tmeta_data\x18\x06 \x01(\tR\bmetaData\x129\n" +
	"\n" +
	"created_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x129\n" +
	"\n" +
	"deleted_at\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\tdeletedAt\"\xc8\x05\n" +
	"\x04Card\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1e\n" +
	"\n" +
	"identifier\x18\x02 \x01(\tR\n" +
	"identifier\x12'\n" +
	"\x0fuser_identifier\x18\x03 \x01(\tR\x0euserIdentifier\x12\x16\n" +
	"\x06status\x18\x04 \x01(\tR\x06status\x12\x10\n" +
	"\x03iin\x18\x05 \x01(\x03R\x03iin\x12\x1b\n" +
	"\tcard_type\x18\x06 \x01(\x03R\bcardType\x12\x19\n" +
	"\biin_code\x18\a \x01(\tR\aiinCode\x12$\n" +
	"\x0ecard_type_code\x18\b \x01(\tR\fcardTypeCode\x12%\n" +
	"\x0eaccount_number\x18\t \x01(\tR\raccountNumber\x12\x1d\n" +
	"\n" +
	"luhn_digit\x18\n" +
	" \x01(\tR\tluhnDigit\x12\x10\n" +
	"\x03cvv\x18\v \x01(\tR\x03cvv\x12\x19\n" +
	"\bpin_code\x18\f \x01(\tR\apinCode\x12\x1f\n" +
	"\vcard_number\x18\r \x01(\tR\n" +
	"cardNumber\x12\x1f\n" +
	"\vexpire_year\x18\x0e \x01(\tR\n" +
	"expireYear\x12!\n" +
	"\fexpire_month\x18\x0f \x01(\tR\vexpireMonth\x12\x1b\n" +
	"\tmeta_data\x18\x10 \x01(\tR\bmetaData\x129\n" +
	"\n" +
	"expires_at\x18\x11 \x01(\v2\x1a.google.protobuf.TimestampR\texpiresAt\x129\n" +
	"\n" +
	"created_at\x18\x12 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x13 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x129\n" +
	"\n" +
	"deleted_at\x18\x14 \x01(\v2\x1a.google.protobuf.TimestampR\tdeletedAt\"\xee\x03\n" +
	"\aCardPAN\x12\x1e\n" +
	"\n" +
	"identifier\x18\x01 \x01(\tR\n" +
	"identifier\x12'\n" +
	"\x0fuser_identifier\x18\x02 \x01(\tR\x0euserIdentifier\x12\x1f\n" +
	"\vholder_name\x18\x03 \x01(\tR\n" +
	"holderName\x12\x16\n" +
	"\x06status\x18\x04 \x01(\tR\x06status\x12\x1f\n" +
	"\vcard_number\x18\x05 \x01(\tR\n" +
	"cardNumber\x12\x10\n" +
	"\x03cvv\x18\x06 \x01(\tR\x03cvv\x12\x1f\n" +
	"\vexpire_year\x18\a \x01(\tR\n" +
	"expireYear\x12!\n" +
	"\fexpire_month\x18\b \x01(\tR\vexpireMonth\x129\n" +
	"\n" +
	"expires_at\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\texpiresAt\x129\n" +
	"\n" +
	"created_at\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\v \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x129\n" +
	"\n" +
	"deleted_at\x18\f \x01(\v2\x1a.google.protobuf.TimestampR\tdeletedAtB%Z#github.com/liveutil/card_service/pbb\x06proto3"

var (
	file_rpc_card_proto_rawDescOnce sync.Once
	file_rpc_card_proto_rawDescData []byte
)

func file_rpc_card_proto_rawDescGZIP() []byte {
	file_rpc_card_proto_rawDescOnce.Do(func() {
		file_rpc_card_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rpc_card_proto_rawDesc), len(file_rpc_card_proto_rawDesc)))
	})
	return file_rpc_card_proto_rawDescData
}

var file_rpc_card_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_rpc_card_proto_goTypes = []any{
	(*IssuerIdentifier)(nil),      // 0: pb.IssuerIdentifier
	(*CardType)(nil),              // 1: pb.CardType
	(*Card)(nil),                  // 2: pb.Card
	(*CardPAN)(nil),               // 3: pb.CardPAN
	(*timestamppb.Timestamp)(nil), // 4: google.protobuf.Timestamp
}
var file_rpc_card_proto_depIdxs = []int32{
	4,  // 0: pb.IssuerIdentifier.expire_date:type_name -> google.protobuf.Timestamp
	4,  // 1: pb.IssuerIdentifier.created_at:type_name -> google.protobuf.Timestamp
	4,  // 2: pb.IssuerIdentifier.updated_at:type_name -> google.protobuf.Timestamp
	4,  // 3: pb.IssuerIdentifier.deleted_at:type_name -> google.protobuf.Timestamp
	4,  // 4: pb.CardType.created_at:type_name -> google.protobuf.Timestamp
	4,  // 5: pb.CardType.updated_at:type_name -> google.protobuf.Timestamp
	4,  // 6: pb.CardType.deleted_at:type_name -> google.protobuf.Timestamp
	4,  // 7: pb.Card.expires_at:type_name -> google.protobuf.Timestamp
	4,  // 8: pb.Card.created_at:type_name -> google.protobuf.Timestamp
	4,  // 9: pb.Card.updated_at:type_name -> google.protobuf.Timestamp
	4,  // 10: pb.Card.deleted_at:type_name -> google.protobuf.Timestamp
	4,  // 11: pb.CardPAN.expires_at:type_name -> google.protobuf.Timestamp
	4,  // 12: pb.CardPAN.created_at:type_name -> google.protobuf.Timestamp
	4,  // 13: pb.CardPAN.updated_at:type_name -> google.protobuf.Timestamp
	4,  // 14: pb.CardPAN.deleted_at:type_name -> google.protobuf.Timestamp
	15, // [15:15] is the sub-list for method output_type
	15, // [15:15] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_rpc_card_proto_init() }
func file_rpc_card_proto_init() {
	if File_rpc_card_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_card_proto_rawDesc), len(file_rpc_card_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_rpc_card_proto_goTypes,
		DependencyIndexes: file_rpc_card_proto_depIdxs,
		MessageInfos:      file_rpc_card_proto_msgTypes,
	}.Build()
	File_rpc_card_proto = out.File
	file_rpc_card_proto_goTypes = nil
	file_rpc_card_proto_depIdxs = nil
}
