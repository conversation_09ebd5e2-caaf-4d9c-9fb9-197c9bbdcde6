// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rpc_admin_dto.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ CARD @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
//
// Request for creating a card
type CreateCardRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// user identifier to create card for
	UserIdentifier string `protobuf:"bytes,1,opt,name=user_identifier,json=userIdentifier,proto3" json:"user_identifier,omitempty"`
	// card status
	Status string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	// card issuer identifier number id
	Iin int64 `protobuf:"varint,3,opt,name=iin,proto3" json:"iin,omitempty"`
	// card issuer identifier code number
	IinCode string `protobuf:"bytes,4,opt,name=iin_code,json=iinCode,proto3" json:"iin_code,omitempty"`
	// card account number
	AccountNumber string `protobuf:"bytes,5,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	// luhn digit
	LuhnDigit string `protobuf:"bytes,6,opt,name=luhn_digit,json=luhnDigit,proto3" json:"luhn_digit,omitempty"`
	// cvv
	Cvv string `protobuf:"bytes,7,opt,name=cvv,proto3" json:"cvv,omitempty"`
	// pin code
	PinCode string `protobuf:"bytes,8,opt,name=pin_code,json=pinCode,proto3" json:"pin_code,omitempty"`
	// card number: iin_code+account_number+luhn_digit
	CardNumber string `protobuf:"bytes,9,opt,name=card_number,json=cardNumber,proto3" json:"card_number,omitempty"`
	// expire year
	ExpireYear string `protobuf:"bytes,10,opt,name=expire_year,json=expireYear,proto3" json:"expire_year,omitempty"`
	// expire month
	ExpireMonth string `protobuf:"bytes,11,opt,name=expire_month,json=expireMonth,proto3" json:"expire_month,omitempty"`
	// meta data
	MetaData string `protobuf:"bytes,12,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`
	// expire date and time of card
	ExpiresAt *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
	// card type
	CardType      string `protobuf:"bytes,14,opt,name=card_type,json=cardType,proto3" json:"card_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCardRequest) Reset() {
	*x = CreateCardRequest{}
	mi := &file_rpc_admin_dto_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCardRequest) ProtoMessage() {}

func (x *CreateCardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_admin_dto_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCardRequest.ProtoReflect.Descriptor instead.
func (*CreateCardRequest) Descriptor() ([]byte, []int) {
	return file_rpc_admin_dto_proto_rawDescGZIP(), []int{0}
}

func (x *CreateCardRequest) GetUserIdentifier() string {
	if x != nil {
		return x.UserIdentifier
	}
	return ""
}

func (x *CreateCardRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *CreateCardRequest) GetIin() int64 {
	if x != nil {
		return x.Iin
	}
	return 0
}

func (x *CreateCardRequest) GetIinCode() string {
	if x != nil {
		return x.IinCode
	}
	return ""
}

func (x *CreateCardRequest) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *CreateCardRequest) GetLuhnDigit() string {
	if x != nil {
		return x.LuhnDigit
	}
	return ""
}

func (x *CreateCardRequest) GetCvv() string {
	if x != nil {
		return x.Cvv
	}
	return ""
}

func (x *CreateCardRequest) GetPinCode() string {
	if x != nil {
		return x.PinCode
	}
	return ""
}

func (x *CreateCardRequest) GetCardNumber() string {
	if x != nil {
		return x.CardNumber
	}
	return ""
}

func (x *CreateCardRequest) GetExpireYear() string {
	if x != nil {
		return x.ExpireYear
	}
	return ""
}

func (x *CreateCardRequest) GetExpireMonth() string {
	if x != nil {
		return x.ExpireMonth
	}
	return ""
}

func (x *CreateCardRequest) GetMetaData() string {
	if x != nil {
		return x.MetaData
	}
	return ""
}

func (x *CreateCardRequest) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

func (x *CreateCardRequest) GetCardType() string {
	if x != nil {
		return x.CardType
	}
	return ""
}

// Response for creating, updating, or deleting a card
type CardActionsTypeResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// whether the request returns error or no.
	Error bool `protobuf:"varint,1,opt,name=error,proto3" json:"error,omitempty"`
	// error message if occurred or success message
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// created card
	Card          *Card `protobuf:"bytes,3,opt,name=card,proto3" json:"card,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CardActionsTypeResponse) Reset() {
	*x = CardActionsTypeResponse{}
	mi := &file_rpc_admin_dto_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CardActionsTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardActionsTypeResponse) ProtoMessage() {}

func (x *CardActionsTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_admin_dto_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardActionsTypeResponse.ProtoReflect.Descriptor instead.
func (*CardActionsTypeResponse) Descriptor() ([]byte, []int) {
	return file_rpc_admin_dto_proto_rawDescGZIP(), []int{1}
}

func (x *CardActionsTypeResponse) GetError() bool {
	if x != nil {
		return x.Error
	}
	return false
}

func (x *CardActionsTypeResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CardActionsTypeResponse) GetCard() *Card {
	if x != nil {
		return x.Card
	}
	return nil
}

// Request for setting card status
type SetCardStatusRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// card identifier
	Identifier string `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// card status
	Status        string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetCardStatusRequest) Reset() {
	*x = SetCardStatusRequest{}
	mi := &file_rpc_admin_dto_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetCardStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetCardStatusRequest) ProtoMessage() {}

func (x *SetCardStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_admin_dto_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetCardStatusRequest.ProtoReflect.Descriptor instead.
func (*SetCardStatusRequest) Descriptor() ([]byte, []int) {
	return file_rpc_admin_dto_proto_rawDescGZIP(), []int{2}
}

func (x *SetCardStatusRequest) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *SetCardStatusRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

// Request for setting card pin code
type SetCardPINRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// card identifier
	Identifier string `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// card pin code
	PinCode       string `protobuf:"bytes,2,opt,name=pin_code,json=pinCode,proto3" json:"pin_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetCardPINRequest) Reset() {
	*x = SetCardPINRequest{}
	mi := &file_rpc_admin_dto_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetCardPINRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetCardPINRequest) ProtoMessage() {}

func (x *SetCardPINRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_admin_dto_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetCardPINRequest.ProtoReflect.Descriptor instead.
func (*SetCardPINRequest) Descriptor() ([]byte, []int) {
	return file_rpc_admin_dto_proto_rawDescGZIP(), []int{3}
}

func (x *SetCardPINRequest) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *SetCardPINRequest) GetPinCode() string {
	if x != nil {
		return x.PinCode
	}
	return ""
}

// Request for setting card meta data
type SetCardMetaDataRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// card identifier
	Identifier string `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// card meta data
	MetaData      string `protobuf:"bytes,2,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetCardMetaDataRequest) Reset() {
	*x = SetCardMetaDataRequest{}
	mi := &file_rpc_admin_dto_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetCardMetaDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetCardMetaDataRequest) ProtoMessage() {}

func (x *SetCardMetaDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_admin_dto_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetCardMetaDataRequest.ProtoReflect.Descriptor instead.
func (*SetCardMetaDataRequest) Descriptor() ([]byte, []int) {
	return file_rpc_admin_dto_proto_rawDescGZIP(), []int{4}
}

func (x *SetCardMetaDataRequest) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *SetCardMetaDataRequest) GetMetaData() string {
	if x != nil {
		return x.MetaData
	}
	return ""
}

// @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ ISSUER IDENTIFIER @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
//
// Request for creating an issuer identifier
type CreateIssuerIdentifierRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// iin number
	Iin string `protobuf:"bytes,1,opt,name=iin,proto3" json:"iin,omitempty"`
	// iin status
	Status string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	// issuer owner user identifier
	IssuerUserIdentifier string `protobuf:"bytes,3,opt,name=issuer_user_identifier,json=issuerUserIdentifier,proto3" json:"issuer_user_identifier,omitempty"`
	// issuer name
	IssuerName string `protobuf:"bytes,4,opt,name=issuer_name,json=issuerName,proto3" json:"issuer_name,omitempty"`
	// issuer logo url
	IssuerLogo string `protobuf:"bytes,5,opt,name=issuer_logo,json=issuerLogo,proto3" json:"issuer_logo,omitempty"`
	// issuer website url
	IssuerUrl string `protobuf:"bytes,6,opt,name=issuer_url,json=issuerUrl,proto3" json:"issuer_url,omitempty"`
	// metadata
	MetaData string `protobuf:"bytes,7,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`
	// expire date
	ExpireDate    *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=expire_date,json=expireDate,proto3" json:"expire_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateIssuerIdentifierRequest) Reset() {
	*x = CreateIssuerIdentifierRequest{}
	mi := &file_rpc_admin_dto_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateIssuerIdentifierRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateIssuerIdentifierRequest) ProtoMessage() {}

func (x *CreateIssuerIdentifierRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_admin_dto_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateIssuerIdentifierRequest.ProtoReflect.Descriptor instead.
func (*CreateIssuerIdentifierRequest) Descriptor() ([]byte, []int) {
	return file_rpc_admin_dto_proto_rawDescGZIP(), []int{5}
}

func (x *CreateIssuerIdentifierRequest) GetIin() string {
	if x != nil {
		return x.Iin
	}
	return ""
}

func (x *CreateIssuerIdentifierRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *CreateIssuerIdentifierRequest) GetIssuerUserIdentifier() string {
	if x != nil {
		return x.IssuerUserIdentifier
	}
	return ""
}

func (x *CreateIssuerIdentifierRequest) GetIssuerName() string {
	if x != nil {
		return x.IssuerName
	}
	return ""
}

func (x *CreateIssuerIdentifierRequest) GetIssuerLogo() string {
	if x != nil {
		return x.IssuerLogo
	}
	return ""
}

func (x *CreateIssuerIdentifierRequest) GetIssuerUrl() string {
	if x != nil {
		return x.IssuerUrl
	}
	return ""
}

func (x *CreateIssuerIdentifierRequest) GetMetaData() string {
	if x != nil {
		return x.MetaData
	}
	return ""
}

func (x *CreateIssuerIdentifierRequest) GetExpireDate() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpireDate
	}
	return nil
}

// Response for creating, updating, or deleting an issuer identifier
type IssuerIdentifierActionsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// whether the request returns error or no.
	Error bool `protobuf:"varint,1,opt,name=error,proto3" json:"error,omitempty"`
	// error message if occurred or success message
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// created card issuer information
	IssuerIdentifier *IssuerIdentifier `protobuf:"bytes,3,opt,name=issuer_identifier,json=issuerIdentifier,proto3" json:"issuer_identifier,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *IssuerIdentifierActionsResponse) Reset() {
	*x = IssuerIdentifierActionsResponse{}
	mi := &file_rpc_admin_dto_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IssuerIdentifierActionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IssuerIdentifierActionsResponse) ProtoMessage() {}

func (x *IssuerIdentifierActionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_admin_dto_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IssuerIdentifierActionsResponse.ProtoReflect.Descriptor instead.
func (*IssuerIdentifierActionsResponse) Descriptor() ([]byte, []int) {
	return file_rpc_admin_dto_proto_rawDescGZIP(), []int{6}
}

func (x *IssuerIdentifierActionsResponse) GetError() bool {
	if x != nil {
		return x.Error
	}
	return false
}

func (x *IssuerIdentifierActionsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *IssuerIdentifierActionsResponse) GetIssuerIdentifier() *IssuerIdentifier {
	if x != nil {
		return x.IssuerIdentifier
	}
	return nil
}

// Request for updating issuer identifier
type UpdateIssuerIdentifierRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// identifier
	Identifier string `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// iin status
	Status string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	// issuer owner user identifier
	IssuerUserIdentifier string `protobuf:"bytes,3,opt,name=issuer_user_identifier,json=issuerUserIdentifier,proto3" json:"issuer_user_identifier,omitempty"`
	// issuer name
	IssuerName string `protobuf:"bytes,4,opt,name=issuer_name,json=issuerName,proto3" json:"issuer_name,omitempty"`
	// issuer logo url
	IssuerLogo string `protobuf:"bytes,5,opt,name=issuer_logo,json=issuerLogo,proto3" json:"issuer_logo,omitempty"`
	// issuer website url
	IssuerUrl string `protobuf:"bytes,6,opt,name=issuer_url,json=issuerUrl,proto3" json:"issuer_url,omitempty"`
	// metadata
	MetaData string `protobuf:"bytes,7,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`
	// expire date
	ExpireDate    *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=expire_date,json=expireDate,proto3" json:"expire_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateIssuerIdentifierRequest) Reset() {
	*x = UpdateIssuerIdentifierRequest{}
	mi := &file_rpc_admin_dto_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateIssuerIdentifierRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateIssuerIdentifierRequest) ProtoMessage() {}

func (x *UpdateIssuerIdentifierRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_admin_dto_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateIssuerIdentifierRequest.ProtoReflect.Descriptor instead.
func (*UpdateIssuerIdentifierRequest) Descriptor() ([]byte, []int) {
	return file_rpc_admin_dto_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateIssuerIdentifierRequest) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *UpdateIssuerIdentifierRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *UpdateIssuerIdentifierRequest) GetIssuerUserIdentifier() string {
	if x != nil {
		return x.IssuerUserIdentifier
	}
	return ""
}

func (x *UpdateIssuerIdentifierRequest) GetIssuerName() string {
	if x != nil {
		return x.IssuerName
	}
	return ""
}

func (x *UpdateIssuerIdentifierRequest) GetIssuerLogo() string {
	if x != nil {
		return x.IssuerLogo
	}
	return ""
}

func (x *UpdateIssuerIdentifierRequest) GetIssuerUrl() string {
	if x != nil {
		return x.IssuerUrl
	}
	return ""
}

func (x *UpdateIssuerIdentifierRequest) GetMetaData() string {
	if x != nil {
		return x.MetaData
	}
	return ""
}

func (x *UpdateIssuerIdentifierRequest) GetExpireDate() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpireDate
	}
	return nil
}

// @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ CARD TYPE @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
//
// Request for creating a card type
type CreateCardTypeRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// card type code
	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	// card type name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// card type description
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// card type metadata
	MetaData string `protobuf:"bytes,4,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`
	// issuer identifier id
	Iin           int64 `protobuf:"varint,5,opt,name=iin,proto3" json:"iin,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCardTypeRequest) Reset() {
	*x = CreateCardTypeRequest{}
	mi := &file_rpc_admin_dto_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCardTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCardTypeRequest) ProtoMessage() {}

func (x *CreateCardTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_admin_dto_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCardTypeRequest.ProtoReflect.Descriptor instead.
func (*CreateCardTypeRequest) Descriptor() ([]byte, []int) {
	return file_rpc_admin_dto_proto_rawDescGZIP(), []int{8}
}

func (x *CreateCardTypeRequest) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *CreateCardTypeRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateCardTypeRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateCardTypeRequest) GetMetaData() string {
	if x != nil {
		return x.MetaData
	}
	return ""
}

func (x *CreateCardTypeRequest) GetIin() int64 {
	if x != nil {
		return x.Iin
	}
	return 0
}

// Request for updating a card type
type UpdateCardTypeRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// card type code
	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	// card type name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// card type description
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// card type metadata
	MetaData      string `protobuf:"bytes,4,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCardTypeRequest) Reset() {
	*x = UpdateCardTypeRequest{}
	mi := &file_rpc_admin_dto_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCardTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCardTypeRequest) ProtoMessage() {}

func (x *UpdateCardTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_admin_dto_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCardTypeRequest.ProtoReflect.Descriptor instead.
func (*UpdateCardTypeRequest) Descriptor() ([]byte, []int) {
	return file_rpc_admin_dto_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateCardTypeRequest) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *UpdateCardTypeRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateCardTypeRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateCardTypeRequest) GetMetaData() string {
	if x != nil {
		return x.MetaData
	}
	return ""
}

// Response for creating, updating, or deleting a card type
type CardTypeActionsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// whether the request returns error or no.
	Error bool `protobuf:"varint,1,opt,name=error,proto3" json:"error,omitempty"`
	// error message if occurred or success message
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// created card type information
	CardType      *CardType `protobuf:"bytes,3,opt,name=card_type,json=cardType,proto3" json:"card_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CardTypeActionsResponse) Reset() {
	*x = CardTypeActionsResponse{}
	mi := &file_rpc_admin_dto_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CardTypeActionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardTypeActionsResponse) ProtoMessage() {}

func (x *CardTypeActionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_admin_dto_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardTypeActionsResponse.ProtoReflect.Descriptor instead.
func (*CardTypeActionsResponse) Descriptor() ([]byte, []int) {
	return file_rpc_admin_dto_proto_rawDescGZIP(), []int{10}
}

func (x *CardTypeActionsResponse) GetError() bool {
	if x != nil {
		return x.Error
	}
	return false
}

func (x *CardTypeActionsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CardTypeActionsResponse) GetCardType() *CardType {
	if x != nil {
		return x.CardType
	}
	return nil
}

// Request for charging card account balance
type ChargeCardAccountBalanceRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// card primary account number (16 digit card no)
	PrimaryAccountNumber string `protobuf:"bytes,1,opt,name=primary_account_number,json=primaryAccountNumber,proto3" json:"primary_account_number,omitempty"`
	// amount to charge
	Amount float64 `protobuf:"fixed64,2,opt,name=amount,proto3" json:"amount,omitempty"`
	// asset to charge
	Asset string `protobuf:"bytes,3,opt,name=asset,proto3" json:"asset,omitempty"`
	// reference
	Reference string `protobuf:"bytes,4,opt,name=reference,proto3" json:"reference,omitempty"`
	// description
	Description string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	// payment id
	PaymentId     string `protobuf:"bytes,6,opt,name=payment_id,json=paymentId,proto3" json:"payment_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChargeCardAccountBalanceRequest) Reset() {
	*x = ChargeCardAccountBalanceRequest{}
	mi := &file_rpc_admin_dto_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChargeCardAccountBalanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChargeCardAccountBalanceRequest) ProtoMessage() {}

func (x *ChargeCardAccountBalanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_admin_dto_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChargeCardAccountBalanceRequest.ProtoReflect.Descriptor instead.
func (*ChargeCardAccountBalanceRequest) Descriptor() ([]byte, []int) {
	return file_rpc_admin_dto_proto_rawDescGZIP(), []int{11}
}

func (x *ChargeCardAccountBalanceRequest) GetPrimaryAccountNumber() string {
	if x != nil {
		return x.PrimaryAccountNumber
	}
	return ""
}

func (x *ChargeCardAccountBalanceRequest) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *ChargeCardAccountBalanceRequest) GetAsset() string {
	if x != nil {
		return x.Asset
	}
	return ""
}

func (x *ChargeCardAccountBalanceRequest) GetReference() string {
	if x != nil {
		return x.Reference
	}
	return ""
}

func (x *ChargeCardAccountBalanceRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ChargeCardAccountBalanceRequest) GetPaymentId() string {
	if x != nil {
		return x.PaymentId
	}
	return ""
}

// Response for charging card account balance
type ChargeAccountBalanceResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// whether the request returns error or no.
	Error bool `protobuf:"varint,1,opt,name=error,proto3" json:"error,omitempty"`
	// error message if occurred or success message
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// transaction id
	TransactionId string `protobuf:"bytes,3,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"`
	// payment id
	PaymentId string `protobuf:"bytes,4,opt,name=payment_id,json=paymentId,proto3" json:"payment_id,omitempty"`
	// reference
	Reference string `protobuf:"bytes,5,opt,name=reference,proto3" json:"reference,omitempty"`
	// description
	Description string `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	// amount
	Amount string `protobuf:"bytes,7,opt,name=amount,proto3" json:"amount,omitempty"`
	// asset
	Asset         string `protobuf:"bytes,8,opt,name=asset,proto3" json:"asset,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChargeAccountBalanceResponse) Reset() {
	*x = ChargeAccountBalanceResponse{}
	mi := &file_rpc_admin_dto_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChargeAccountBalanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChargeAccountBalanceResponse) ProtoMessage() {}

func (x *ChargeAccountBalanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_admin_dto_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChargeAccountBalanceResponse.ProtoReflect.Descriptor instead.
func (*ChargeAccountBalanceResponse) Descriptor() ([]byte, []int) {
	return file_rpc_admin_dto_proto_rawDescGZIP(), []int{12}
}

func (x *ChargeAccountBalanceResponse) GetError() bool {
	if x != nil {
		return x.Error
	}
	return false
}

func (x *ChargeAccountBalanceResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ChargeAccountBalanceResponse) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *ChargeAccountBalanceResponse) GetPaymentId() string {
	if x != nil {
		return x.PaymentId
	}
	return ""
}

func (x *ChargeAccountBalanceResponse) GetReference() string {
	if x != nil {
		return x.Reference
	}
	return ""
}

func (x *ChargeAccountBalanceResponse) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ChargeAccountBalanceResponse) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *ChargeAccountBalanceResponse) GetAsset() string {
	if x != nil {
		return x.Asset
	}
	return ""
}

var File_rpc_admin_dto_proto protoreflect.FileDescriptor

const file_rpc_admin_dto_proto_rawDesc = "" +
	"\n" +
	"\x13rpc_admin_dto.proto\x12\x02pb\x1a\x0erpc_card.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xce\x03\n" +
	"\x11CreateCardRequest\x12'\n" +
	"\x0fuser_identifier\x18\x01 \x01(\tR\x0euserIdentifier\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\x12\x10\n" +
	"\x03iin\x18\x03 \x01(\x03R\x03iin\x12\x19\n" +
	"\biin_code\x18\x04 \x01(\tR\aiinCode\x12%\n" +
	"\x0eaccount_number\x18\x05 \x01(\tR\raccountNumber\x12\x1d\n" +
	"\n" +
	"luhn_digit\x18\x06 \x01(\tR\tluhnDigit\x12\x10\n" +
	"\x03cvv\x18\a \x01(\tR\x03cvv\x12\x19\n" +
	"\bpin_code\x18\b \x01(\tR\apinCode\x12\x1f\n" +
	"\vcard_number\x18\t \x01(\tR\n" +
	"cardNumber\x12\x1f\n" +
	"\vexpire_year\x18\n" +
	" \x01(\tR\n" +
	"expireYear\x12!\n" +
	"\fexpire_month\x18\v \x01(\tR\vexpireMonth\x12\x1b\n" +
	"\tmeta_data\x18\f \x01(\tR\bmetaData\x129\n" +
	"\n" +
	"expires_at\x18\r \x01(\v2\x1a.google.protobuf.TimestampR\texpiresAt\x12\x1b\n" +
	"\tcard_type\x18\x0e \x01(\tR\bcardType\"g\n" +
	"\x17CardActionsTypeResponse\x12\x14\n" +
	"\x05error\x18\x01 \x01(\bR\x05error\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x1c\n" +
	"\x04card\x18\x03 \x01(\v2\b.pb.CardR\x04card\"N\n" +
	"\x14SetCardStatusRequest\x12\x1e\n" +
	"\n" +
	"identifier\x18\x01 \x01(\tR\n" +
	"identifier\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\"N\n" +
	"\x11SetCardPINRequest\x12\x1e\n" +
	"\n" +
	"identifier\x18\x01 \x01(\tR\n" +
	"identifier\x12\x19\n" +
	"\bpin_code\x18\x02 \x01(\tR\apinCode\"U\n" +
	"\x16SetCardMetaDataRequest\x12\x1e\n" +
	"\n" +
	"identifier\x18\x01 \x01(\tR\n" +
	"identifier\x12\x1b\n" +
	"\tmeta_data\x18\x02 \x01(\tR\bmetaData\"\xba\x02\n" +
	"\x1dCreateIssuerIdentifierRequest\x12\x10\n" +
	"\x03iin\x18\x01 \x01(\tR\x03iin\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\x124\n" +
	"\x16issuer_user_identifier\x18\x03 \x01(\tR\x14issuerUserIdentifier\x12\x1f\n" +
	"\vissuer_name\x18\x04 \x01(\tR\n" +
	"issuerName\x12\x1f\n" +
	"\vissuer_logo\x18\x05 \x01(\tR\n" +
	"issuerLogo\x12\x1d\n" +
	"\n" +
	"issuer_url\x18\x06 \x01(\tR\tissuerUrl\x12\x1b\n" +
	"\tmeta_data\x18\a \x01(\tR\bmetaData\x12;\n" +
	"\vexpire_date\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"expireDate\"\x94\x01\n" +
	"\x1fIssuerIdentifierActionsResponse\x12\x14\n" +
	"\x05error\x18\x01 \x01(\bR\x05error\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12A\n" +
	"\x11issuer_identifier\x18\x03 \x01(\v2\x14.pb.IssuerIdentifierR\x10issuerIdentifier\"\xc8\x02\n" +
	"\x1dUpdateIssuerIdentifierRequest\x12\x1e\n" +
	"\n" +
	"identifier\x18\x01 \x01(\tR\n" +
	"identifier\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\x124\n" +
	"\x16issuer_user_identifier\x18\x03 \x01(\tR\x14issuerUserIdentifier\x12\x1f\n" +
	"\vissuer_name\x18\x04 \x01(\tR\n" +
	"issuerName\x12\x1f\n" +
	"\vissuer_logo\x18\x05 \x01(\tR\n" +
	"issuerLogo\x12\x1d\n" +
	"\n" +
	"issuer_url\x18\x06 \x01(\tR\tissuerUrl\x12\x1b\n" +
	"\tmeta_data\x18\a \x01(\tR\bmetaData\x12;\n" +
	"\vexpire_date\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"expireDate\"\x90\x01\n" +
	"\x15CreateCardTypeRequest\x12\x12\n" +
	"\x04code\x18\x01 \x01(\tR\x04code\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x1b\n" +
	"\tmeta_data\x18\x04 \x01(\tR\bmetaData\x12\x10\n" +
	"\x03iin\x18\x05 \x01(\x03R\x03iin\"~\n" +
	"\x15UpdateCardTypeRequest\x12\x12\n" +
	"\x04code\x18\x01 \x01(\tR\x04code\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x1b\n" +
	"\tmeta_data\x18\x04 \x01(\tR\bmetaData\"t\n" +
	"\x17CardTypeActionsResponse\x12\x14\n" +
	"\x05error\x18\x01 \x01(\bR\x05error\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12)\n" +
	"\tcard_type\x18\x03 \x01(\v2\f.pb.CardTypeR\bcardType\"\xe4\x01\n" +
	"\x1fChargeCardAccountBalanceRequest\x124\n" +
	"\x16primary_account_number\x18\x01 \x01(\tR\x14primaryAccountNumber\x12\x16\n" +
	"\x06amount\x18\x02 \x01(\x01R\x06amount\x12\x14\n" +
	"\x05asset\x18\x03 \x01(\tR\x05asset\x12\x1c\n" +
	"\treference\x18\x04 \x01(\tR\treference\x12 \n" +
	"\vdescription\x18\x05 \x01(\tR\vdescription\x12\x1d\n" +
	"\n" +
	"payment_id\x18\x06 \x01(\tR\tpaymentId\"\x82\x02\n" +
	"\x1cChargeAccountBalanceResponse\x12\x14\n" +
	"\x05error\x18\x01 \x01(\bR\x05error\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12%\n" +
	"\x0etransaction_id\x18\x03 \x01(\tR\rtransactionId\x12\x1d\n" +
	"\n" +
	"payment_id\x18\x04 \x01(\tR\tpaymentId\x12\x1c\n" +
	"\treference\x18\x05 \x01(\tR\treference\x12 \n" +
	"\vdescription\x18\x06 \x01(\tR\vdescription\x12\x16\n" +
	"\x06amount\x18\a \x01(\tR\x06amount\x12\x14\n" +
	"\x05asset\x18\b \x01(\tR\x05assetB%Z#github.com/liveutil/card_service/pbb\x06proto3"

var (
	file_rpc_admin_dto_proto_rawDescOnce sync.Once
	file_rpc_admin_dto_proto_rawDescData []byte
)

func file_rpc_admin_dto_proto_rawDescGZIP() []byte {
	file_rpc_admin_dto_proto_rawDescOnce.Do(func() {
		file_rpc_admin_dto_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rpc_admin_dto_proto_rawDesc), len(file_rpc_admin_dto_proto_rawDesc)))
	})
	return file_rpc_admin_dto_proto_rawDescData
}

var file_rpc_admin_dto_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_rpc_admin_dto_proto_goTypes = []any{
	(*CreateCardRequest)(nil),               // 0: pb.CreateCardRequest
	(*CardActionsTypeResponse)(nil),         // 1: pb.CardActionsTypeResponse
	(*SetCardStatusRequest)(nil),            // 2: pb.SetCardStatusRequest
	(*SetCardPINRequest)(nil),               // 3: pb.SetCardPINRequest
	(*SetCardMetaDataRequest)(nil),          // 4: pb.SetCardMetaDataRequest
	(*CreateIssuerIdentifierRequest)(nil),   // 5: pb.CreateIssuerIdentifierRequest
	(*IssuerIdentifierActionsResponse)(nil), // 6: pb.IssuerIdentifierActionsResponse
	(*UpdateIssuerIdentifierRequest)(nil),   // 7: pb.UpdateIssuerIdentifierRequest
	(*CreateCardTypeRequest)(nil),           // 8: pb.CreateCardTypeRequest
	(*UpdateCardTypeRequest)(nil),           // 9: pb.UpdateCardTypeRequest
	(*CardTypeActionsResponse)(nil),         // 10: pb.CardTypeActionsResponse
	(*ChargeCardAccountBalanceRequest)(nil), // 11: pb.ChargeCardAccountBalanceRequest
	(*ChargeAccountBalanceResponse)(nil),    // 12: pb.ChargeAccountBalanceResponse
	(*timestamppb.Timestamp)(nil),           // 13: google.protobuf.Timestamp
	(*Card)(nil),                            // 14: pb.Card
	(*IssuerIdentifier)(nil),                // 15: pb.IssuerIdentifier
	(*CardType)(nil),                        // 16: pb.CardType
}
var file_rpc_admin_dto_proto_depIdxs = []int32{
	13, // 0: pb.CreateCardRequest.expires_at:type_name -> google.protobuf.Timestamp
	14, // 1: pb.CardActionsTypeResponse.card:type_name -> pb.Card
	13, // 2: pb.CreateIssuerIdentifierRequest.expire_date:type_name -> google.protobuf.Timestamp
	15, // 3: pb.IssuerIdentifierActionsResponse.issuer_identifier:type_name -> pb.IssuerIdentifier
	13, // 4: pb.UpdateIssuerIdentifierRequest.expire_date:type_name -> google.protobuf.Timestamp
	16, // 5: pb.CardTypeActionsResponse.card_type:type_name -> pb.CardType
	6,  // [6:6] is the sub-list for method output_type
	6,  // [6:6] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_rpc_admin_dto_proto_init() }
func file_rpc_admin_dto_proto_init() {
	if File_rpc_admin_dto_proto != nil {
		return
	}
	file_rpc_card_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_admin_dto_proto_rawDesc), len(file_rpc_admin_dto_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_rpc_admin_dto_proto_goTypes,
		DependencyIndexes: file_rpc_admin_dto_proto_depIdxs,
		MessageInfos:      file_rpc_admin_dto_proto_msgTypes,
	}.Build()
	File_rpc_admin_dto_proto = out.File
	file_rpc_admin_dto_proto_goTypes = nil
	file_rpc_admin_dto_proto_depIdxs = nil
}
