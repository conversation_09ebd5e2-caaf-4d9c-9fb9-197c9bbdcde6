// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rpc_order_dto.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Order is the main object that represents an order in the system.
type Order struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// unique external identifier for internal-external system identifier isolation
	Identifier string `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// customer user external identifier
	UserIdentifier string `protobuf:"bytes,2,opt,name=user_identifier,json=userIdentifier,proto3" json:"user_identifier,omitempty"`
	// card external identifier
	CardIdentifier string `protobuf:"bytes,3,opt,name=card_identifier,json=cardIdentifier,proto3" json:"card_identifier,omitempty"`
	// card holder name
	CardHolderName string `protobuf:"bytes,4,opt,name=card_holder_name,json=cardHolderName,proto3" json:"card_holder_name,omitempty"`
	// card pan number
	CardPan string `protobuf:"bytes,5,opt,name=card_pan,json=cardPan,proto3" json:"card_pan,omitempty"`
	// target domain for order
	TargetDomain string `protobuf:"bytes,6,opt,name=target_domain,json=targetDomain,proto3" json:"target_domain,omitempty"`
	// order status determinable value
	Status string `protobuf:"bytes,7,opt,name=status,proto3" json:"status,omitempty"`
	// order type
	OrderType string `protobuf:"bytes,8,opt,name=order_type,json=orderType,proto3" json:"order_type,omitempty"`
	// order amount
	OrderAmount string `protobuf:"bytes,9,opt,name=order_amount,json=orderAmount,proto3" json:"order_amount,omitempty"`
	// order asset
	OrderAsset string `protobuf:"bytes,10,opt,name=order_asset,json=orderAsset,proto3" json:"order_asset,omitempty"`
	// order description
	OrderDescription string `protobuf:"bytes,11,opt,name=order_description,json=orderDescription,proto3" json:"order_description,omitempty"`
	// order reference to make it idempotent
	Reference string `protobuf:"bytes,12,opt,name=reference,proto3" json:"reference,omitempty"`
	// voucher code for order
	VoucherCode string `protobuf:"bytes,13,opt,name=voucher_code,json=voucherCode,proto3" json:"voucher_code,omitempty"`
	// when order was created
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// when order was updated
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// when order was deleted
	DeletedAt     *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Order) Reset() {
	*x = Order{}
	mi := &file_rpc_order_dto_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Order) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Order) ProtoMessage() {}

func (x *Order) ProtoReflect() protoreflect.Message {
	mi := &file_rpc_order_dto_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Order.ProtoReflect.Descriptor instead.
func (*Order) Descriptor() ([]byte, []int) {
	return file_rpc_order_dto_proto_rawDescGZIP(), []int{0}
}

func (x *Order) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *Order) GetUserIdentifier() string {
	if x != nil {
		return x.UserIdentifier
	}
	return ""
}

func (x *Order) GetCardIdentifier() string {
	if x != nil {
		return x.CardIdentifier
	}
	return ""
}

func (x *Order) GetCardHolderName() string {
	if x != nil {
		return x.CardHolderName
	}
	return ""
}

func (x *Order) GetCardPan() string {
	if x != nil {
		return x.CardPan
	}
	return ""
}

func (x *Order) GetTargetDomain() string {
	if x != nil {
		return x.TargetDomain
	}
	return ""
}

func (x *Order) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Order) GetOrderType() string {
	if x != nil {
		return x.OrderType
	}
	return ""
}

func (x *Order) GetOrderAmount() string {
	if x != nil {
		return x.OrderAmount
	}
	return ""
}

func (x *Order) GetOrderAsset() string {
	if x != nil {
		return x.OrderAsset
	}
	return ""
}

func (x *Order) GetOrderDescription() string {
	if x != nil {
		return x.OrderDescription
	}
	return ""
}

func (x *Order) GetReference() string {
	if x != nil {
		return x.Reference
	}
	return ""
}

func (x *Order) GetVoucherCode() string {
	if x != nil {
		return x.VoucherCode
	}
	return ""
}

func (x *Order) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Order) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Order) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

var File_rpc_order_dto_proto protoreflect.FileDescriptor

const file_rpc_order_dto_proto_rawDesc = "" +
	"\n" +
	"\x13rpc_order_dto.proto\x12\x02pb\x1a\x1fgoogle/protobuf/timestamp.proto\"\xfd\x04\n" +
	"\x05Order\x12\x1e\n" +
	"\n" +
	"identifier\x18\x01 \x01(\tR\n" +
	"identifier\x12'\n" +
	"\x0fuser_identifier\x18\x02 \x01(\tR\x0euserIdentifier\x12'\n" +
	"\x0fcard_identifier\x18\x03 \x01(\tR\x0ecardIdentifier\x12(\n" +
	"\x10card_holder_name\x18\x04 \x01(\tR\x0ecardHolderName\x12\x19\n" +
	"\bcard_pan\x18\x05 \x01(\tR\acardPan\x12#\n" +
	"\rtarget_domain\x18\x06 \x01(\tR\ftargetDomain\x12\x16\n" +
	"\x06status\x18\a \x01(\tR\x06status\x12\x1d\n" +
	"\n" +
	"order_type\x18\b \x01(\tR\torderType\x12!\n" +
	"\forder_amount\x18\t \x01(\tR\vorderAmount\x12\x1f\n" +
	"\vorder_asset\x18\n" +
	" \x01(\tR\n" +
	"orderAsset\x12+\n" +
	"\x11order_description\x18\v \x01(\tR\x10orderDescription\x12\x1c\n" +
	"\treference\x18\f \x01(\tR\treference\x12!\n" +
	"\fvoucher_code\x18\r \x01(\tR\vvoucherCode\x129\n" +
	"\n" +
	"created_at\x18\x0e \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x0f \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x129\n" +
	"\n" +
	"deleted_at\x18\x10 \x01(\v2\x1a.google.protobuf.TimestampR\tdeletedAtB&Z$github.com/liveutil/order_service/pbb\x06proto3"

var (
	file_rpc_order_dto_proto_rawDescOnce sync.Once
	file_rpc_order_dto_proto_rawDescData []byte
)

func file_rpc_order_dto_proto_rawDescGZIP() []byte {
	file_rpc_order_dto_proto_rawDescOnce.Do(func() {
		file_rpc_order_dto_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_rpc_order_dto_proto_rawDesc), len(file_rpc_order_dto_proto_rawDesc)))
	})
	return file_rpc_order_dto_proto_rawDescData
}

var file_rpc_order_dto_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_rpc_order_dto_proto_goTypes = []any{
	(*Order)(nil),                 // 0: pb.Order
	(*timestamppb.Timestamp)(nil), // 1: google.protobuf.Timestamp
}
var file_rpc_order_dto_proto_depIdxs = []int32{
	1, // 0: pb.Order.created_at:type_name -> google.protobuf.Timestamp
	1, // 1: pb.Order.updated_at:type_name -> google.protobuf.Timestamp
	1, // 2: pb.Order.deleted_at:type_name -> google.protobuf.Timestamp
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_rpc_order_dto_proto_init() }
func file_rpc_order_dto_proto_init() {
	if File_rpc_order_dto_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_order_dto_proto_rawDesc), len(file_rpc_order_dto_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_rpc_order_dto_proto_goTypes,
		DependencyIndexes: file_rpc_order_dto_proto_depIdxs,
		MessageInfos:      file_rpc_order_dto_proto_msgTypes,
	}.Build()
	File_rpc_order_dto_proto = out.File
	file_rpc_order_dto_proto_goTypes = nil
	file_rpc_order_dto_proto_depIdxs = nil
}
