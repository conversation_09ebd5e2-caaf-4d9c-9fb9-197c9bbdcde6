// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rpc_order_service.proto

package pb

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_rpc_order_service_proto protoreflect.FileDescriptor

const file_rpc_order_service_proto_rawDesc = "" +
	"\n" +
	"\x17rpc_order_service.proto\x12\x02pb\x1a\x1cgoogle/api/annotations.proto\x1a.protoc-gen-openapiv2/options/annotations.proto\x1a\rrpc_dto.proto2\xa6\x04\n" +
	"\fOrderService\x12\\\n" +
	"\bGetOrder\x12\x16.pb.OrderActionRequest\x1a\x18.pb.OrderActionsResponse\"\x1e\x82\xd3\xe4\x93\x02\x18\x12\x16/v1/orders/{reference}\x12\x92\x01\n" +
	"\x11ContextUserOrders\x12\x1c.pb.ContextUserOrdersRequest\x1a\x1d.pb.ContextUserOrdersResponse\"@\x82\xd3\xe4\x93\x02:\x128/v1/orders/context_user_orders/{page_size}/{page_offset}\x12`\n" +
	"\n" +
	"PlaceOrder\x12\x15.pb.PlaceOrderRequest\x1a\x18.pb.OrderActionsResponse\"!\x82\xd3\xe4\x93\x02\x1b:\x01*\"\x16/v1/orders/place_order\x12V\n" +
	"\bPayOrder\x12\x13.pb.PayOrderRequest\x1a\x14.pb.PayOrderResponse\"\x1f\x82\xd3\xe4\x93\x02\x19:\x01*\"\x14/v1/orders/pay_order\x12i\n" +
	"\vCancelOrder\x12\x16.pb.OrderActionRequest\x1a\x18.pb.OrderActionsResponse\"(\x82\xd3\xe4\x93\x02\":\x01*\"\x1d/v1/orders/cancel/{reference}B\xce\x02\x92A\xa4\x02\x12\x90\x01\n" +
	"\x11Order Service API\x12BOrder Service API for managing user orders and related operations.\"0\n" +
	"\x19Order Service API Support\x1a\x13liveutil@icloud.com2\x051.0.0*\x02\x01\x022\x10application/json:\x10application/jsonZY\n" +
	"W\n" +
	"\x06Bearer\x12M\b\x02\x128Authentication token, prefixed by Bearer: Bearer <token>\x1a\rAuthorization \x02b\f\n" +
	"\n" +
	"\n" +
	"\x06Bearer\x12\x00Z$github.com/liveutil/order_service/pbb\x06proto3"

var file_rpc_order_service_proto_goTypes = []any{
	(*OrderActionRequest)(nil),        // 0: pb.OrderActionRequest
	(*ContextUserOrdersRequest)(nil),  // 1: pb.ContextUserOrdersRequest
	(*PlaceOrderRequest)(nil),         // 2: pb.PlaceOrderRequest
	(*PayOrderRequest)(nil),           // 3: pb.PayOrderRequest
	(*OrderActionsResponse)(nil),      // 4: pb.OrderActionsResponse
	(*ContextUserOrdersResponse)(nil), // 5: pb.ContextUserOrdersResponse
	(*PayOrderResponse)(nil),          // 6: pb.PayOrderResponse
}
var file_rpc_order_service_proto_depIdxs = []int32{
	0, // 0: pb.OrderService.GetOrder:input_type -> pb.OrderActionRequest
	1, // 1: pb.OrderService.ContextUserOrders:input_type -> pb.ContextUserOrdersRequest
	2, // 2: pb.OrderService.PlaceOrder:input_type -> pb.PlaceOrderRequest
	3, // 3: pb.OrderService.PayOrder:input_type -> pb.PayOrderRequest
	0, // 4: pb.OrderService.CancelOrder:input_type -> pb.OrderActionRequest
	4, // 5: pb.OrderService.GetOrder:output_type -> pb.OrderActionsResponse
	5, // 6: pb.OrderService.ContextUserOrders:output_type -> pb.ContextUserOrdersResponse
	4, // 7: pb.OrderService.PlaceOrder:output_type -> pb.OrderActionsResponse
	6, // 8: pb.OrderService.PayOrder:output_type -> pb.PayOrderResponse
	4, // 9: pb.OrderService.CancelOrder:output_type -> pb.OrderActionsResponse
	5, // [5:10] is the sub-list for method output_type
	0, // [0:5] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_rpc_order_service_proto_init() }
func file_rpc_order_service_proto_init() {
	if File_rpc_order_service_proto != nil {
		return
	}
	file_rpc_dto_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_order_service_proto_rawDesc), len(file_rpc_order_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_rpc_order_service_proto_goTypes,
		DependencyIndexes: file_rpc_order_service_proto_depIdxs,
	}.Build()
	File_rpc_order_service_proto = out.File
	file_rpc_order_service_proto_goTypes = nil
	file_rpc_order_service_proto_depIdxs = nil
}
