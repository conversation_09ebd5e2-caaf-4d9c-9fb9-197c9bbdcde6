// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: rpc_card_admin_service.proto

package pb

import (
	_ "github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2/options"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_rpc_card_admin_service_proto protoreflect.FileDescriptor

const file_rpc_card_admin_service_proto_rawDesc = "" +
	"\n" +
	"\x1crpc_card_admin_service.proto\x12\x02pb\x1a\x1cgoogle/api/annotations.proto\x1a.protoc-gen-openapiv2/options/annotations.proto\x1a\x13rpc_admin_dto.proto2\xd3\b\n" +
	"\x19CardAdministrationService\x12\x8f\x01\n" +
	"\x16CreateIssuerIdentifier\x12!.pb.CreateIssuerIdentifierRequest\x1a#.pb.IssuerIdentifierActionsResponse\"-\x82\xd3\xe4\x93\x02':\x01*\"\"/v1/admin/issuer_identifier/create\x12\x8f\x01\n" +
	"\x16UpdateIssuerIdentifier\x12!.pb.UpdateIssuerIdentifierRequest\x1a#.pb.IssuerIdentifierActionsResponse\"-\x82\xd3\xe4\x93\x02':\x01*\"\"/v1/admin/issuer_identifier/update\x12o\n" +
	"\x0eCreateCardType\x12\x19.pb.CreateCardTypeRequest\x1a\x1b.pb.CardTypeActionsResponse\"%\x82\xd3\xe4\x93\x02\x1f:\x01*\"\x1a/v1/admin/card_type/create\x12o\n" +
	"\x0eUpdateCardType\x12\x19.pb.UpdateCardTypeRequest\x1a\x1b.pb.CardTypeActionsResponse\"%\x82\xd3\xe4\x93\x02\x1f:\x01*\"\x1a/v1/admin/card_type/update\x12b\n" +
	"\n" +
	"CreateCard\x12\x15.pb.CreateCardRequest\x1a\x1b.pb.CardActionsTypeResponse\" \x82\xd3\xe4\x93\x02\x1a:\x01*\"\x15/v1/admin/card/create\x12l\n" +
	"\rSetCardStatus\x12\x18.pb.SetCardStatusRequest\x1a\x1b.pb.CardActionsTypeResponse\"$\x82\xd3\xe4\x93\x02\x1e:\x01*\"\x19/v1/admin/card/set_status\x12s\n" +
	"\x0fSetCardMetaData\x12\x1a.pb.SetCardMetaDataRequest\x1a\x1b.pb.CardActionsTypeResponse\"'\x82\xd3\xe4\x93\x02!:\x01*\"\x1c/v1/admin/card/set_meta_data\x12c\n" +
	"\n" +
	"SetCardPIN\x12\x15.pb.SetCardPINRequest\x1a\x1b.pb.CardActionsTypeResponse\"!\x82\xd3\xe4\x93\x02\x1b:\x01*\"\x16/v1/admin/card/set_pin\x12\x83\x01\n" +
	"\x18ChargeCardAccountBalance\x12#.pb.ChargeCardAccountBalanceRequest\x1a .pb.ChargeAccountBalanceResponse\" \x82\xd3\xe4\x93\x02\x1a:\x01*\"\x15/v1/admin/card/chargeB\xf6\x02\x92A\xcd\x02\x12\xb9\x01\n" +
	"\x1fCard Administration Service API\x12OCard Administration Service API for managing user cards and related operations.\">\n" +
	"'Card Administration Service API Support\x1a\x13liveutil@icloud.com2\x051.0.0*\x02\x01\x022\x10application/json:\x10application/jsonZY\n" +
	"W\n" +
	"\x06Bearer\x12M\b\x02\x128Authentication token, prefixed by Bearer: Bearer <token>\x1a\rAuthorization \x02b\f\n" +
	"\n" +
	"\n" +
	"\x06Bearer\x12\x00Z#github.com/liveutil/card_service/pbb\x06proto3"

var file_rpc_card_admin_service_proto_goTypes = []any{
	(*CreateIssuerIdentifierRequest)(nil),   // 0: pb.CreateIssuerIdentifierRequest
	(*UpdateIssuerIdentifierRequest)(nil),   // 1: pb.UpdateIssuerIdentifierRequest
	(*CreateCardTypeRequest)(nil),           // 2: pb.CreateCardTypeRequest
	(*UpdateCardTypeRequest)(nil),           // 3: pb.UpdateCardTypeRequest
	(*CreateCardRequest)(nil),               // 4: pb.CreateCardRequest
	(*SetCardStatusRequest)(nil),            // 5: pb.SetCardStatusRequest
	(*SetCardMetaDataRequest)(nil),          // 6: pb.SetCardMetaDataRequest
	(*SetCardPINRequest)(nil),               // 7: pb.SetCardPINRequest
	(*ChargeCardAccountBalanceRequest)(nil), // 8: pb.ChargeCardAccountBalanceRequest
	(*IssuerIdentifierActionsResponse)(nil), // 9: pb.IssuerIdentifierActionsResponse
	(*CardTypeActionsResponse)(nil),         // 10: pb.CardTypeActionsResponse
	(*CardActionsTypeResponse)(nil),         // 11: pb.CardActionsTypeResponse
	(*ChargeAccountBalanceResponse)(nil),    // 12: pb.ChargeAccountBalanceResponse
}
var file_rpc_card_admin_service_proto_depIdxs = []int32{
	0,  // 0: pb.CardAdministrationService.CreateIssuerIdentifier:input_type -> pb.CreateIssuerIdentifierRequest
	1,  // 1: pb.CardAdministrationService.UpdateIssuerIdentifier:input_type -> pb.UpdateIssuerIdentifierRequest
	2,  // 2: pb.CardAdministrationService.CreateCardType:input_type -> pb.CreateCardTypeRequest
	3,  // 3: pb.CardAdministrationService.UpdateCardType:input_type -> pb.UpdateCardTypeRequest
	4,  // 4: pb.CardAdministrationService.CreateCard:input_type -> pb.CreateCardRequest
	5,  // 5: pb.CardAdministrationService.SetCardStatus:input_type -> pb.SetCardStatusRequest
	6,  // 6: pb.CardAdministrationService.SetCardMetaData:input_type -> pb.SetCardMetaDataRequest
	7,  // 7: pb.CardAdministrationService.SetCardPIN:input_type -> pb.SetCardPINRequest
	8,  // 8: pb.CardAdministrationService.ChargeCardAccountBalance:input_type -> pb.ChargeCardAccountBalanceRequest
	9,  // 9: pb.CardAdministrationService.CreateIssuerIdentifier:output_type -> pb.IssuerIdentifierActionsResponse
	9,  // 10: pb.CardAdministrationService.UpdateIssuerIdentifier:output_type -> pb.IssuerIdentifierActionsResponse
	10, // 11: pb.CardAdministrationService.CreateCardType:output_type -> pb.CardTypeActionsResponse
	10, // 12: pb.CardAdministrationService.UpdateCardType:output_type -> pb.CardTypeActionsResponse
	11, // 13: pb.CardAdministrationService.CreateCard:output_type -> pb.CardActionsTypeResponse
	11, // 14: pb.CardAdministrationService.SetCardStatus:output_type -> pb.CardActionsTypeResponse
	11, // 15: pb.CardAdministrationService.SetCardMetaData:output_type -> pb.CardActionsTypeResponse
	11, // 16: pb.CardAdministrationService.SetCardPIN:output_type -> pb.CardActionsTypeResponse
	12, // 17: pb.CardAdministrationService.ChargeCardAccountBalance:output_type -> pb.ChargeAccountBalanceResponse
	9,  // [9:18] is the sub-list for method output_type
	0,  // [0:9] is the sub-list for method input_type
	0,  // [0:0] is the sub-list for extension type_name
	0,  // [0:0] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_rpc_card_admin_service_proto_init() }
func file_rpc_card_admin_service_proto_init() {
	if File_rpc_card_admin_service_proto != nil {
		return
	}
	file_rpc_admin_dto_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_rpc_card_admin_service_proto_rawDesc), len(file_rpc_card_admin_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_rpc_card_admin_service_proto_goTypes,
		DependencyIndexes: file_rpc_card_admin_service_proto_depIdxs,
	}.Build()
	File_rpc_card_admin_service_proto = out.File
	file_rpc_card_admin_service_proto_goTypes = nil
	file_rpc_card_admin_service_proto_depIdxs = nil
}
