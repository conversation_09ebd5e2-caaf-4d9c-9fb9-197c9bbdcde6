syntax = "proto3";

package pb;

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";
import "rpc_dto.proto";

option go_package = "github.com/liveutil/order_service/pb";

option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  info: {
    title: "Order Service API";
    version: "1.0.0";
    description: "Order Service API for managing user orders and related operations.";
    contact: {
      name: "Order Service API Support";
      email: "<EMAIL>";
    };
  };
  schemes: HTTP;
  schemes: HTTPS;
  consumes: "application/json";
  produces: "application/json";
  security_definitions: {
    security: {
      key: "Bearer";
      value: {
        type: TYPE_API_KEY;
        in: IN_HEADER;
        name: "Authorization";
        description: "Authentication token, prefixed by Bearer: Bearer <token>";
      };
    };
  };
  security: {
    security_requirement: {
      key: "Bearer";
      value: {};
    };
  };
};

// Order Service API for managing user orders and related operations.
service OrderService {
  // Get context user order by specified reference
  rpc GetOrder(OrderActionRequest) returns (OrderActionsResponse) {
    option (google.api.http) = {
      get: "/v1/orders/{reference}"
    };
  }
	// Get context user orders list
	rpc ContextUserOrders(ContextUserOrdersRequest) returns (ContextUserOrdersResponse) {
		option (google.api.http) = {
			get: "/v1/orders/context_user_orders/{page_size}/{page_offset}"
		};
	}

  // Place an order
  rpc PlaceOrder(PlaceOrderRequest) returns (OrderActionsResponse) {
    option (google.api.http) = {
      post: "/v1/orders/place_order"
      body: "*"
    };
  }

  // Pay Order
  rpc PayOrder(PayOrderRequest) returns (PayOrderResponse) {
    option (google.api.http) = {
      post: "/v1/orders/pay_order"
      body: "*"
    };
  }

  // Cancel Order
  rpc CancelOrder(OrderActionRequest) returns (OrderActionsResponse) {
    option (google.api.http) = {
      post: "/v1/orders/cancel/{reference}"
      body: "*"
    };
  }
}