syntax = "proto3";

package pb;

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";
import "rpc_admin_dto.proto";

option go_package = "github.com/liveutil/card_service/pb";

option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  info: {
    title: "Card Administration Service API";
    version: "1.0.0";
    description: "Card Administration Service API for managing user cards and related operations.";
    contact: {
      name: "Card Administration Service API Support";
      email: "<EMAIL>";
    };
  };
  schemes: HTTP;
  schemes: HTTPS;
  consumes: "application/json";
  produces: "application/json";
  security_definitions: {
    security: {
      key: "Bearer";
      value: {
        type: TYPE_API_KEY;
        in: IN_HEADER;
        name: "Authorization";
        description: "Authentication token, prefixed by Bearer: Bearer <token>";
      };
    };
  };
  security: {
    security_requirement: {
      key: "Bearer";
      value: {};
    };
  };
};

// Card Administration Service API for managing user cards and related operations.
service CardAdministrationService {
  // Create a new issuer identifier
  rpc CreateIssuerIdentifier(CreateIssuerIdentifierRequest) returns (IssuerIdentifierActionsResponse) {
    option (google.api.http) = {
      post: "/v1/admin/issuer_identifier/create"
      body: "*"
    };
  }

  // Update an existing issuer identifier
  rpc UpdateIssuerIdentifier(UpdateIssuerIdentifierRequest) returns (IssuerIdentifierActionsResponse) {
    option (google.api.http) = {
      post: "/v1/admin/issuer_identifier/update"
      body: "*"
    };
  }

  rpc CreateCardType(CreateCardTypeRequest) returns (CardTypeActionsResponse) {
    option (google.api.http) = {
      post: "/v1/admin/card_type/create"
      body: "*"
    };
  }

  rpc UpdateCardType(UpdateCardTypeRequest) returns (CardTypeActionsResponse) {
    option (google.api.http) = {
      post: "/v1/admin/card_type/update"
      body: "*"
    };
  }

  // Create a new card for a user
  rpc CreateCard(CreateCardRequest) returns (CardActionsTypeResponse) {
    option (google.api.http) = {
      post: "/v1/admin/card/create"
      body: "*"
    };
  }

  rpc SetCardStatus(SetCardStatusRequest) returns (CardActionsTypeResponse) {
    option (google.api.http) = {
      post: "/v1/admin/card/set_status"
      body: "*"
    };
  }

  rpc SetCardMetaData(SetCardMetaDataRequest) returns (CardActionsTypeResponse) {
    option (google.api.http) = {
      post: "/v1/admin/card/set_meta_data"
      body: "*"
    };
  }

  rpc SetCardPIN(SetCardPINRequest) returns (CardActionsTypeResponse) {
    option (google.api.http) = {
      post: "/v1/admin/card/set_pin"
      body: "*"
    };
  }

  rpc ChargeCardAccountBalance(ChargeCardAccountBalanceRequest) returns (ChargeAccountBalanceResponse) {
    option (google.api.http) = {
      post: "/v1/admin/card/charge"
      body: "*"
    };
  }
}