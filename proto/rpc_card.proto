syntax = "proto3";

package pb;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/liveutil/card_service/pb";

// issuer_identifier contains 'System Master Data' for IIN (Issuer Identification Number) registry
// It stores information about card issuers, including their unique identifiers, names, logos, and URLs.
// The model is designed to manage the metadata associated with each issuer, allowing for easy retrieval and updates.
// The model includes fields for the issuer's IIN, external identifier, name, logo URL, website URL, and additional metadata.
// It also includes timestamps for creation, updates, and deletion, enabling tracking of the lifecycle of each issuer record.
// The model is indexed on various fields to optimize query performance and ensure efficient data retrieval.
// The model structure is as follows:
message IssuerIdentifier {
  int64 id = 1;
  // unique external identifier for internal-external system identifier isolation
  string identifier = 2;
  // iin number
  string iin = 3;
  // iin status
  string status = 4;
  // issuer owner user identifier
  string issuer_identifier = 5;
  // issuer name
  string issuer_name = 6;
  // issuer logo url
  string issuer_logo = 7;
  // issuer website url
  string issuer_url = 8;
  // metadata
  string meta_data = 9;
  // expire date
  google.protobuf.Timestamp expire_date = 10;
  // creation time
  google.protobuf.Timestamp created_at = 11;
  // update time
  google.protobuf.Timestamp updated_at = 12;
  // deletion time
  google.protobuf.Timestamp deleted_at = 13;
}

// CardType model contains 'System Master Data' for card types
// It stores information about different card types, including their unique identifiers, codes, names, descriptions, and metadata.
// The model is designed to manage the metadata associated with each card type, allowing for easy retrieval and updates.
// The model includes fields for the card type's code, name, description, and additional metadata.
// It also includes timestamps for creation, updates, and deletion, enabling tracking of the lifecycle of each card type record.
message CardType {
  int64 id = 1;
  // unique external identifier for internal-external system identifier isolation
  string identifier = 2;
  // card type code
  string code = 3;
  // card type name
  string name = 4;
  // card type description
  string description = 5;
  // card type metadata
  string meta_data = 6;
  // creation time
  google.protobuf.Timestamp created_at = 7;
  // update time
  google.protobuf.Timestamp updated_at = 8;
  // deletion time
  google.protobuf.Timestamp deleted_at = 9;
}

// Card model contains 'System Master Data' for cards
// It stores all the necessary information about each card, including its status, identifiers, and metadata.
// The model is designed to handle various card statuses and includes fields for card details such as IIN, account number, CVV, pin code, card number, expiration date, and metadata.
// The model also includes timestamps for creation, updates, and deletion, allowing for tracking the lifecycle of each card.
// The model is indexed on various fields to optimize query performance and ensure efficient data retrieval.
// The model structure is as follows: Card PAN details
message Card {
  int64 id = 1;
  // unique external identifier for internal-external system identifier isolation
  string identifier = 2;
  // owner of card external identifier
  string user_identifier = 3;
  // card status determinable value
  string status = 4;
  // card issuer identifier number id
  int64 iin = 5;
  // card type id
  int64 card_type = 6;
  // card issuer identifier code number
  string iin_code = 7;
  // card type code
  string card_type_code = 8;
  // card account number
  string account_number = 9;
  // luhn digit
  string luhn_digit = 10;
  // cvv
  string cvv = 11;
  // pin code
  string pin_code = 12;
  // card number: iin_code(4)+card_type(1)+account_number(10)+luhn_digit(1)
  string card_number = 13;
  // expire year
  string expire_year = 14;
  // expire month
  string expire_month = 15;
  // meta data
  string meta_data = 16;
  // expire date and time of card
  google.protobuf.Timestamp expires_at = 17;
  // creation time
  google.protobuf.Timestamp created_at = 18;
  // update time
  google.protobuf.Timestamp updated_at = 19;
  // deletion time
  google.protobuf.Timestamp deleted_at = 20;
}


message CardPAN {
  // unique external identifier for internal-external system identifier isolation
  string identifier = 1;
  // owner of card external identifier
  string user_identifier = 2;
  // holder name
  string holder_name = 3;
  // card status determinable value
  string status = 4;
  // card number: iin_code+account_number+luhn_digit
  string card_number = 5;
  // cvv
  string cvv = 6;
  // expire year
  string expire_year = 7;
  // expire month
  string expire_month = 8;
  // expire date and time of card
  google.protobuf.Timestamp expires_at = 9;
  // creation time
  google.protobuf.Timestamp created_at = 10;
  // update time
  google.protobuf.Timestamp updated_at = 11;
  // deletion time
  google.protobuf.Timestamp deleted_at = 12;
}