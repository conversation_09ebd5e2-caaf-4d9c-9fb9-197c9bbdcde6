syntax = "proto3";

package pb;

import "google/api/annotations.proto";
import "protoc-gen-openapiv2/options/annotations.proto";
import "rpc_dto.proto";

// option go_package = "./proto;card_service";
option go_package = "github.com/liveutil/card_service/pb";

option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  info: {
    title: "Card Service API";
    version: "1.0.0";
    description: "Card Service API for managing user cards and related operations.";
    contact: {
      name: "Card Service API Support";
      email: "<EMAIL>";
    };
  };
  schemes: HTTP;
  schemes: HTTPS;
  consumes: "application/json";
  produces: "application/json";
  security_definitions: {
    security: {
      key: "Bearer";
      value: {
        type: TYPE_API_KEY;
        in: IN_HEADER;
        name: "Authorization";
        description: "Authentication token, prefixed by Bearer: Bearer <token>";
      };
    };
  };
  security: {
    security_requirement: {
      key: "Bearer";
      value: {};
    };
  };
};

// Card Service API for managing user cards and related operations.
service CardService {
	// Get user context data
	rpc ContextUserCards(Empty) returns (ContextUserCardsResponse) {
		option (google.api.http) = {
			get: "/v1/card/context_get_cards"
		};
	}

	// Validate card details
	rpc ValidateCard(ValidateCardRequest) returns (ValidateCardResponse) {
		option (google.api.http) = {
			post: "/v1/card/validate_card"
			body: "*"
		};
	}
}