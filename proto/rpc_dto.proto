syntax = "proto3";

package pb;

option go_package = "github.com/liveutil/card_service/pb";

import "rpc_card.proto";

// Response for getting context user cards list
message ContextUserCardsResponse {
  // whether the request returns error or no.
  bool error = 1;
  // error message if occurred or success message
  string message = 2;
  // list of user cards
  repeated CardPAN cards = 3;
}

// Request for validating a card
message ValidateCardRequest {
  // card number to validate
  string card_number = 1;
  // card holder name to validate
  string cvv = 2;
  // card expiration month to validate
  string expire_month = 3;
  // card expiration year to validate
  string expire_year = 4;
}

// Response for validating a card
message ValidateCardResponse {
  // whether the request returns error or no.
  bool error = 1;
  // error message if occurred or success message
  string message = 2;
  // whether the card is valid or not
  bool is_valid = 3;
  // card details if the card is valid
  CardPAN card = 4;
}

// Define an empty message for methods that require no input.
message Empty {}