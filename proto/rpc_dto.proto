syntax = "proto3";

package pb;

option go_package = "github.com/liveutil/order_service/pb";

import "rpc_order_dto.proto";

// ContextUserOrdersRequest
message ContextUserOrdersRequest {
  // page size
  int32 page_size = 1;
  // page offset
  int32 page_offset = 2;
}

// Response for getting context user orders list
message ContextUserOrdersResponse {
  // whether the request returns error or no.
  bool error = 1;
  // error message if occurred or success message
  string message = 2;
  // result array length
  int32 result_length = 3;
  // list of user orders
  repeated Order orders = 4;
}

// Request for placing an order
message PlaceOrderRequest {
  // card pan to be charged
  string card_pan = 1;
  // amount to be charged
  string amount = 2;
  // asset to be charged
  string asset = 3;
  // reference for idempotency
  string reference = 4;
  // description for order
  string description = 5;
  // target domain for order
  string target_domain = 6;
  // voucher code to be applied
  string voucher_code = 7;
}

// Response for placing an order
message OrderActionsResponse {
  // whether the request returns error or no.
  bool error = 1;
  // error message if occurred or success message
  string message = 2;
  // created order
  Order order = 3;
}

// PayOrderRequest
message PayOrderRequest {
  // order reference
  string reference = 1;
  // gateway identifier
  string gateway = 2;
}

// PayOrderResponse
message PayOrderResponse {
  // whether the request returns error or no.
  bool error = 1;
  // error message if occurred or success message
  string message = 2;
  // generated payment gateway url
  string url = 3;
}

// OrderAction Request
message OrderActionRequest {
  // reference for order
  string reference = 1;
}

// Define an empty message for methods that require no input.
message Empty {}
