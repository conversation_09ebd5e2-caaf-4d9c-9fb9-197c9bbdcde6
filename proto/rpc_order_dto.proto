syntax = "proto3";

package pb;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/liveutil/order_service/pb";


// Order is the main object that represents an order in the system.
message Order {
    // unique external identifier for internal-external system identifier isolation
    string identifier = 1;
    // customer user external identifier
    string user_identifier = 2;
    // card external identifier
    string card_identifier = 3;
    // card holder name
    string card_holder_name = 4;
    // card pan number
    string card_pan = 5;
    // target domain for order
    string target_domain = 6;
    // order status determinable value
    string status = 7;
    // order type
    string order_type = 8;
    // order amount
    string order_amount = 9;
    // order asset
    string order_asset = 10;
    // order description
    string order_description = 11;
    // order reference to make it idempotent
    string reference = 12;
    // voucher code for order
    string voucher_code = 13;
    // when order was created
    google.protobuf.Timestamp created_at = 14;
    // when order was updated
    google.protobuf.Timestamp updated_at = 15;
    // when order was deleted
    google.protobuf.Timestamp deleted_at = 16;
}