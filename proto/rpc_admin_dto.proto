syntax = "proto3";

package pb;

option go_package = "github.com/liveutil/card_service/pb";

import "rpc_card.proto";
import "google/protobuf/timestamp.proto";


//
//
// @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ CARD @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
//
// Request for creating a card
message CreateCardRequest {
  // user identifier to create card for
  string user_identifier = 1;
  // card status
  string status = 2;
  // card issuer identifier number id
  int64 iin = 3;
  // card issuer identifier code number
  string iin_code = 4;
  // card account number
  string account_number = 5;
  // luhn digit
  string luhn_digit = 6;
  // cvv
  string cvv = 7;
  // pin code
  string pin_code = 8;
  // card number: iin_code+account_number+luhn_digit
  string card_number = 9;
  // expire year
  string expire_year = 10;
  // expire month
  string expire_month = 11;
  // meta data
  string meta_data = 12;
  // expire date and time of card
  google.protobuf.Timestamp expires_at = 13;
  // card type
  string card_type = 14;
}

// Response for creating, updating, or deleting a card
message CardActionsTypeResponse {
  // whether the request returns error or no.
  bool error = 1;
  // error message if occurred or success message
  string message = 2;
  // created card
  Card card = 3;
}

// Request for setting card status
message SetCardStatusRequest {
  // card identifier
  string identifier = 1;
  // card status
  string status = 2;
}

// Request for setting card pin code
message SetCardPINRequest {
  // card identifier
  string identifier = 1;
  // card pin code
  string pin_code = 2;
}

// Request for setting card meta data
message SetCardMetaDataRequest {
  // card identifier
  string identifier = 1;
  // card meta data
  string meta_data = 2;
}

//
//
// @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ ISSUER IDENTIFIER @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
//
// Request for creating an issuer identifier
message CreateIssuerIdentifierRequest {
  // iin number
  string iin = 1;
  // iin status
  string status = 2;
  // issuer owner user identifier
  string issuer_user_identifier = 3;
  // issuer name
  string issuer_name = 4;
  // issuer logo url
  string issuer_logo = 5;
  // issuer website url
  string issuer_url = 6;
  // metadata
  string meta_data = 7;
  // expire date
  google.protobuf.Timestamp expire_date = 8;
}

// Response for creating, updating, or deleting an issuer identifier
message IssuerIdentifierActionsResponse {
  // whether the request returns error or no.
  bool error = 1;
  // error message if occurred or success message
  string message = 2;
  // created card issuer information
  IssuerIdentifier issuer_identifier = 3;
}

// Request for updating issuer identifier
message UpdateIssuerIdentifierRequest {
  // identifier
  string identifier = 1;
  // iin status
  string status = 2;
  // issuer owner user identifier
  string issuer_user_identifier = 3;
  // issuer name
  string issuer_name = 4;
  // issuer logo url
  string issuer_logo = 5;
  // issuer website url
  string issuer_url = 6;
  // metadata
  string meta_data = 7;
  // expire date
  google.protobuf.Timestamp expire_date = 8;
}

//
//
//
// @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ CARD TYPE @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@
//
// Request for creating a card type
message CreateCardTypeRequest {
  // card type code
  string code = 1;
  // card type name
  string name = 2;
  // card type description
  string description = 3;
  // card type metadata
  string meta_data = 4;
  // issuer identifier id
  int64 iin = 5;
}

// Request for updating a card type
message UpdateCardTypeRequest {
  // card type code
  string code = 1;
  // card type name
  string name = 2;
  // card type description
  string description = 3;
  // card type metadata
  string meta_data = 4;
}

// Response for creating, updating, or deleting a card type
message CardTypeActionsResponse {
  // whether the request returns error or no.
  bool error = 1;
  // error message if occurred or success message
  string message = 2;
  // created card type information
  CardType card_type = 3;
}

// Request for charging card account balance
message ChargeCardAccountBalanceRequest {
  // card primary account number (16 digit card no)
  string primary_account_number = 1;
  // amount to charge
  double amount = 2;
  // asset to charge
  string asset = 3;
  // reference
  string reference = 4;
  // description
  string description = 5;
  // payment id
  string payment_id = 6;
}

// Response for charging card account balance
message ChargeAccountBalanceResponse {
  // whether the request returns error or no.
  bool error = 1;
  // error message if occurred or success message
  string message = 2;
  // transaction id
  string transaction_id = 3;
  // payment id
  string payment_id = 4;
  // reference
  string reference = 5;
  // description
  string description = 6;
  // amount
  string amount = 7;
  // asset
  string asset = 8;
}