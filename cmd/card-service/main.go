package main

import (
	"context"
	"errors"
	"fmt"
	"net"
	"net/http"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	dapr "github.com/dapr/go-sdk/client"
	formancesdkgo "github.com/formancehq/formance-sdk-go/v3"
	kitprometheus "github.com/go-kit/kit/metrics/prometheus"
	kitLog "github.com/go-kit/log"
	"github.com/google/uuid"
	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/liveutil/card_service/internal/application/admin"
	"github.com/liveutil/card_service/internal/application/card"
	"github.com/liveutil/card_service/internal/application/health"
	"github.com/liveutil/card_service/internal/config"
	"github.com/liveutil/card_service/internal/domain/ledger"
	"github.com/liveutil/card_service/internal/infrastructure/db/postgres"
	pb "github.com/liveutil/card_service/pb"
	"github.com/liveutil/go-lib/configuration"
	"github.com/liveutil/go-lib/env"
	"github.com/liveutil/go-lib/grpcutil"
	"github.com/liveutil/go-lib/jsonschema"
	"github.com/liveutil/go-lib/paseto"
	"github.com/liveutil/go-lib/pgutil"
	"github.com/liveutil/go-lib/servicemesh"
	"github.com/liveutil/go-lib/tracing"
	"github.com/liveutil/go-lib/writer"
	"github.com/nats-io/nats.go"
	stdprometheus "github.com/prometheus/client_golang/prometheus"
	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/reflection"
	"google.golang.org/protobuf/encoding/protojson"
)

const VERSION string = "0.0.1"
const APPLICATION string = "card_service"

var fieldKeys = []string{"method"}

func main() {
	// determine which configuration file to use
	configFile := "dev"
	if env.IsProduction() {
		configFile = "prod"
	}

	// load configurations from environment variables file
	conf, err := configuration.LoadEnvConfig[config.Configuration](".", configFile, "env")
	if err != nil {
		log.Fatal().Err(err).Msgf("cannot load config: %v", err)
	}

	// initialize Jaeger tracer
	// @ToDo: add tracing to all services
	tracingProvider, err := tracing.InitJaegerTracing(&tracing.JaegerConfig{
		Endpoint:          conf.JaegerHost,
		ServiceName:       APPLICATION,
		ServiceVersion:    "1.00",
		ServiceInstanceID: uuid.NewString(),
	})
	if err != nil {
		log.Fatal().Err(err).Msgf("failed to initialize Jaeger tracer: %v", err)
	}

	defer func() {
		if err := tracingProvider.Shutdown(context.Background()); err != nil {
			log.Fatal().Err(err).Msgf("failed to shutdown Jaeger tracer: %v", err)
		}
	}()

	jaegerTracer := tracingProvider.Tracer(APPLICATION)

	// initialize nats connection to write logs to NATS in development mode and send events to NATS in production mode
	natsConn, err := nats.Connect(conf.NatsConnection)
	if err != nil {
		log.Fatal().Err(err).Msgf("failed to connect to nats: %v", err)
	}
	defer natsConn.Close()

	// initialize mongo client to write logs to MongoDB in development mode
	mongoClient, err := mongo.Connect(context.Background(), options.Client().ApplyURI(conf.LogsMongoUri))
	if err != nil {
		log.Fatal().Err(err).Msgf("failed to connect to mongo: %v", err)
	}
	defer mongoClient.Disconnect(context.Background())

	// initialize logger to write logs to console in development mode
	if conf.Environment == "development" {
		log.Logger = log.Output(zerolog.ConsoleWriter{Out: os.Stderr})
	}

	// initialize logger
	logger := initLogger(&conf)

	// initialize Dapr client to retrieve secrets from Dapr secret store
	// daprClient, err := dapr.NewClientWithAddress(conf.DaprHost)
	// if err != nil {
	// 	log.Fatal().Err(err).Msgf("failed to create Dapr client: %v", err)
	// }
	// defer daprClient.Close()

	// // retrieve a secret from Dapr secret store
	// ctx, cancel := context.WithTimeout(context.Background(), 100*time.Second)
	// defer cancel()

	// secret, err := getSecret(ctx, daprClient, conf.DaprSecretStoreName, "TOKEN_SYMMETRIC_KEY")
	// if err != nil {
	// 	log.Fatal().Err(err).Msgf("failed to retrieve secret from Dapr: %v", err)
	// }

	// if len(secret) == 0 {
	// 	log.Fatal().Err(err).Msgf("TOKEN_SYMMETRIC_KEY secret is empty")
	// }

	// set token symmetric key from Dapr secret store
	// conf.TokenSymmetricKey = secret

	// initialize 'PASETO' token maker
	maker, err := paseto.NewPasetoMaker(conf.TokenSymmetricKey, conf.Issuer, conf.Audience)
	if err != nil {
		log.Fatal().Err(err).Msgf("failed to create 'PASETO' token maker: %v", err)
	}

	// initialize postgres pool connection.
	dbTimeout := time.Duration(conf.DBConnectionTimeout) * time.Second
	pg := pgutil.NewOrGetSingleton(conf.DBMaxPoolSize, conf.DBConnectionAttempt, dbTimeout, conf.DBSource, logger)
	defer pg.Close()

	go func(pool *pgxpool.Pool) {
		for {
			if e := pool.Ping(context.Background()); e != nil {
				_ = logger.Log("component", "postgres_connection", "error", e)
				log.Err(e).Msgf("postgres_connection: %v", e)
			}
			time.Sleep(time.Second * 5)
		}
	}(pg.Pool)

	// initialize store using postgres pool
	repo := postgres.NewStore(pg.Pool)

	// initialize redis client
	redisClient, err := initRedis(&conf)
	if err != nil {
		log.Fatal().Err(err).Msgf("failed to initialize Redis client: %v", err)
	}
	defer redisClient.Close()

	counter := kitprometheus.NewCounterFrom(stdprometheus.CounterOpts{
		Namespace: "grpc",
		Subsystem: APPLICATION,
		Name:      "request_count",
		Help:      "num of requests received.",
	}, fieldKeys)

	latency := kitprometheus.NewSummaryFrom(stdprometheus.SummaryOpts{
		Namespace: "grpc",
		Subsystem: APPLICATION,
		Name:      "request_latency_microseconds",
		Help:      "total duration of requests (ms).",
	}, fieldKeys)

	// create user service mesh nats client
	userMeshServiceClient := servicemesh.NewNatsUserMeshServiceClient(natsConn)

	// Create Formance client with proper configuration
	formanceClient := formancesdkgo.New(
		formancesdkgo.WithServerURL(conf.FormanceURL),
	)

	err = prepareFormance(context.Background(), &conf, formanceClient)
	if err != nil {
		log.Fatal().Err(err).Msgf("failed to prepare Formance: %v", err)
	}

	opts := &card.CardServiceOpts{
		Repository:       repo,
		Config:           &conf,
		Redis:            redisClient,
		PASETO:           maker,
		NATS:             natsConn,
		UsersServiceMesh: userMeshServiceClient,
		SchemaPath:       jsonschema.GetSchemaPath(APPLICATION),
		ApplicationName:  APPLICATION,
		Logger:           logger,
		FormanceClient:   formanceClient,
	}

	// logging interceptor
	loggingInterceptor := grpcutil.NewLoggingInterceptor(logger, jaegerTracer)

	// instrumented interceptor
	instrumentedInterceptor := grpcutil.NewInstrumentingInterceptor(counter, latency, jaegerTracer)

	// validation interceptor
	validationInterceptor, err := grpcutil.NewValidationInterceptor(jsonschema.GetSchemaPath(APPLICATION), jaegerTracer)
	if err != nil {
		log.Fatal().Err(err).Msgf("failed to create validation interceptor: %v", err)
	}

	// paseto auth interceptor
	authInterceptor := grpcutil.NewPasetoAuthInterceptor(redisClient, grpcutil.AuthenticationOptions{
		HealthCheckKey: conf.HealthCheckKey,
		Secret:         conf.TokenSymmetricKey,
		GetUserInfo: func(ctx context.Context, id int64) (interface{}, error) {
			return userMeshServiceClient.GetSafeUserByID(ctx, id)
		},
		Paseto: maker,
		AccessRoles: map[string][]string{
			"Check":                  {},
			"Watch":                  {},
			"ValidateCard":           {"USER"},
			"ContextUserCards":       {"USER"},
			"CreateCard":             {"ADMIN"},
			"SetCardStatus":          {"ADMIN"},
			"SetCardMetaData":        {"ADMIN"},
			"SetCardPIN":             {"ADMIN"},
			"CreateIssuerIdentifier": {"SUPER_ADMIN"},
			"UpdateIssuerIdentifier": {"SUPER_ADMIN"},
			"CreateCardType":         {"SUPER_ADMIN"},
			"UpdateCardType":         {"SUPER_ADMIN"},
		},
		Tracer: jaegerTracer,
	})

	// initialize gRPC server
	server := grpc.NewServer(
		grpc.ChainUnaryInterceptor(
			loggingInterceptor.LoggingInterceptor(),
			instrumentedInterceptor.InstrumentingInterceptor(),
			validationInterceptor.ValidationInterceptor(),
			authInterceptor.PasetoAuthInterceptor(),
		),
	)

	// register service
	service, err := card.NewCardService(opts)
	if err != nil {
		log.Fatal().Err(err).Msgf("failed to create card service: %v", err)
	}
	pb.RegisterCardServiceServer(server, service)

	// register admin service
	adminOpts := &admin.CardAdministrationServiceOpts{
		Repository:       repo,
		Config:           &conf,
		Redis:            redisClient,
		NATS:             natsConn,
		UsersServiceMesh: userMeshServiceClient,
		SchemaPath:       jsonschema.GetSchemaPath(APPLICATION),
		ApplicationName:  APPLICATION,
		Logger:           logger,
		PASETO:           maker,
		Tracer:           jaegerTracer,
		FormanceClient:   formanceClient,
	}

	adminService, err := admin.NewCardAdministrationService(adminOpts)
	if err != nil {
		log.Fatal().Err(err).Msgf("failed to create card administration service: %v", err)
	}

	pb.RegisterCardAdministrationServiceServer(server, adminService)

	// initialize health service options
	healthOpts := &health.HealthServiceOpts{
		Database: pg.Pool,
		Redis:    redisClient,
		Mongo:    mongoClient,
		NATS:     natsConn,
	}

	healthService := health.NewHealthGRPCService(healthOpts)

	pb.RegisterHealthServer(server, healthService)

	// enable gRPC server reflection in development mode
	if !env.IsProduction() {
		reflection.Register(server)
	}

	// initialize gRPC listener
	listener, err := net.Listen("tcp", conf.GrpcListenerHost)
	if err != nil {
		log.Fatal().Err(err).Msgf("failed to listen: %v", err)
	}

	// start gRPC server
	go func() {
		if err := server.Serve(listener); err != nil {
			log.Fatal().Err(err).Msgf("failed to serve: %v", err)
		}
	}()

	log.Info().Msgf("%s v(%s) gRPC server started on %s", APPLICATION, VERSION, conf.GrpcListenerHost)

	// Create HTTP gateway server
	if conf.HttpListenerHost != "" {
		// Create a new gRPC-Gateway mux with snake_case field names
		gatewayMux := runtime.NewServeMux(
			runtime.WithMarshalerOption(runtime.MIMEWildcard, &runtime.JSONPb{
				MarshalOptions: protojson.MarshalOptions{
					UseProtoNames:   true,
					EmitUnpopulated: true,
				},
			}),
		)

		// Register the gateway endpoints
		opts := []grpc.DialOption{grpc.WithTransportCredentials(insecure.NewCredentials())}
		err = pb.RegisterCardServiceHandlerFromEndpoint(context.Background(), gatewayMux, conf.GrpcListenerHost, opts)
		if err != nil {
			log.Fatal().Err(err).Msgf("failed to register gateway: %v", err)
		}

		// Create HTTP server
		httpServer := &http.Server{
			Addr:    conf.HttpListenerHost,
			Handler: gatewayMux,
		}

		// Start HTTP gateway server
		go func() {
			log.Info().Msgf("%s v(%s) HTTP gateway server started on %s", APPLICATION, VERSION, conf.HttpListenerHost)
			if err := httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
				log.Fatal().Err(err).Msgf("failed to serve HTTP gateway: %v", err)
			}
		}()
	}

	// start User Mesh Service
	cardMeshService := card.NewMeshService(opts, adminService)
	cardMeshContext, cancel := context.WithCancel(context.Background())
	defer cancel()

	if err := cardMeshService.Start(cardMeshContext, []string{
		servicemesh.CARD_SERVICE_GENERATE_DEFAULT_CARD_FOR_USER,
		servicemesh.CARD_SERVICE_GET_SAFE_CARDS_BY_USER_IDENTIFIER,
		servicemesh.CARD_SERVICE_GET_SAFE_CARD_BY_CARD_NUMBER,
		servicemesh.CARD_SERVICE_GET_SAFE_CARD_TYPE_BY_TYPE_CODE,
		servicemesh.CARD_SERVICE_GET_SAFE_ISSUER_IDENTIFIER_BY_IDENTIFIER,
		servicemesh.CARD_SERVICE_GET_SAFE_ISSUER_IDENTIFIER_BY_IIN,
	}); err != nil {
		log.Fatal().Err(err).Msgf("failed to start Card Mesh Service: %v", err)
	}

	// Set up a signal channel to capture shutdown signals
	shutdownChan := make(chan os.Signal, 1)
	signal.Notify(shutdownChan, syscall.SIGINT, syscall.SIGTERM)

	// Wait for a shutdown signal
	<-shutdownChan

	log.Info().Msg("shutdown signal received, stopping server...")

	// Stop the server gracefully
	server.GracefulStop()

	log.Info().Msgf("%s v(%s) gracefully stopped.", APPLICATION, VERSION)
}

func getSecret(ctx context.Context, client dapr.Client, secretStore string, secretKey string) (string, error) {
	secrets, err := client.GetSecret(ctx, secretStore, secretKey, nil)
	if err != nil {
		return "", fmt.Errorf("error retrieving secret '%s'.'%s': %w", secretStore, secretKey, err)
	}

	secret, ok := secrets[secretKey]
	if !ok {
		return "", fmt.Errorf("secret %s not found", secretKey)
	}
	return secret, nil
}

/*
initLogger initializes and returns a logger based on the provided configuration.

Parameters:
- config (*utils.Config): The configuration object containing the logger settings.

Returns:
- kitLog.Logger: The initialized logger.

Example:

	config := &utils.Config{
		LogDestination:         "mongo",
		LogsMongoUri:           "mongodb://localhost:27017",
		LogsDestinationLabel:   "logs",
	}
	logger := initLogger(config)

Note:
- The logger is initialized based on the value of the 'LogDestination' field in the configuration object.
- If 'LogDestination' is set to "stdout", the logger will write logs to standard output.
- If 'LogDestination' is set to "stderr", the logger will write logs to standard error.
- If 'LogDestination' is set to "mongo", the logger will write logs to a MongoDB database specified by 'LogsMongoUri' and 'LogsDestinationLabel'.
- If 'LogDestination' is set to "nats", the logger will write logs to a NATS server specified by 'LogsNatsConnection' and 'LogsDestinationLabel'.
- If 'LogDestination' is not recognized or not provided, the logger will write logs to standard error using the Logfmt format.

Note that the logger returned by this function includes a timestamp field with the current UTC time.
*/
func initLogger(config *config.Configuration) kitLog.Logger {
	var logger kitLog.Logger

	switch config.LogDestination {
	case "stdout":
		logger = kitLog.NewJSONLogger(os.Stdout)

	case "stderr":
		logger = kitLog.NewJSONLogger(os.Stderr)

	case "mongo":
		ctx, cancel := context.WithTimeout(context.TODO(), 5*time.Second)
		defer cancel()

		c, err := mongo.Connect(ctx, options.Client().ApplyURI(config.LogsMongoUri))
		if err != nil {
			log.Fatal().Err(err).Msg("cannot connect to nats")
		}

		logger = kitLog.NewJSONLogger(writer.NewMongoLogWriter(c, config.LogsDestinationLabel))

	case "nats":
		ln, err := nats.Connect(config.NatsConnection)
		if err != nil {
			log.Fatal().Err(err).Msg("cannot connect to nats")
		}
		logger = kitLog.NewJSONLogger(writer.NewNatsLogWriter(ln, config.LogsDestinationLabel))

	default:
		logger = kitLog.NewLogfmtLogger(kitLog.NewSyncWriter(os.Stderr))
	}

	return kitLog.With(logger, "ts", kitLog.DefaultTimestampUTC)
}

/*
initRedis initializes and returns a Redis client based on the provided configuration.

Parameters:
- config (*utils.Config): The configuration object containing the Redis settings.

Returns:
- *redis.Client: The initialized Redis client.
- error: An error if the initialization fails.

Example:

	config := &utils.Config{
		RedisHost:     "localhost:6379",
		RedisPassword: "redispassword",
		RedisDB:       0,
	}
	redisClient, err := initRedis(config)
	if err != nil {
		log.Fatal().Err(err).Msg("failed to initialize Redis client")
	}

This function creates a new Redis client using the Redis options specified in the configuration.
It sets a key-value pair in Redis with the key "delsa" and the value of the constant VERSION.
It then retrieves the value associated with the key "delsa" from Redis and compares it with the constant VERSION.
If the values match, the initialization is considered successful and the Redis client is returned.
Otherwise, an error is returned.

Note: The Redis client must be closed manually when no longer needed.
*/
func initRedis(config *config.Configuration) (*redis.Client, error) {
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	redisClient := redis.NewClient(&redis.Options{
		Addr:     config.RedisHost,
		Password: config.RedisPassword,
		DB:       config.RedisDB,
	})

	err := redisClient.Set(ctx, "delsa", VERSION, 0).Err()
	if err != nil {
		return nil, err
	}

	v, err := redisClient.Get(ctx, "delsa").Result()
	if err != nil {
		return nil, err
	}

	if v != VERSION {
		return nil, err
	}

	return redisClient, nil
}

func prepareFormance(ctx context.Context, config *config.Configuration, formanceClient *formancesdkgo.Formance) error {
	// STEP 1: Check server info
	fmt.Println("Checking server info...")
	ledgerServerInfo, err := ledger.GetServerInfo(ctx, formanceClient)
	if err != nil {
		return fmt.Errorf("get server info error: %v", err)
	}
	log.Printf("Ledger Server Version: %v", ledgerServerInfo.GetV2ConfigInfoResponse().Version)

	// STEP 2: List existing ledgers (should be empty in fresh environment)
	fmt.Println("\nListing existing ledgers...")
	listLedgers, err := ledger.ListLedgers(ctx, formanceClient)
	if err != nil {
		return fmt.Errorf("get list ledgers: %v", err)
	}
	if len(listLedgers.V2LedgerListResponse.GetCursor().Data) == 0 {
		return errors.New("no existing ledgers found (fresh environment confirmed)")
	} else {
		fmt.Println("Found existing ledgers:")
		for index, ledger := range listLedgers.V2LedgerListResponse.GetCursor().Data {
			log.Printf("   %d: %v - bucket: %s", index, ledger.Name, ledger.Bucket)
		}
	}

	// STEP 3: Create credit ledger
	fmt.Println("\nCreating credit ledger...")
	newLedger, err := ledger.CreateLedger(ctx, formanceClient, config.DefaultNetworkCreditLedger, map[string]string{
		"description": "main internal ledger",
	})
	if err != nil {
		if strings.Contains(err.Error(), "LEDGER_ALREADY_EXISTS") {
			log.Printf("Credit ledger already exists: %v", err)
			return nil
		}
		return fmt.Errorf("create credit ledger error: %v", err)
	} else {
		log.Printf("Credit ledger created successfully: %s", newLedger.RawResponse.Status)
	}

	return nil
}
