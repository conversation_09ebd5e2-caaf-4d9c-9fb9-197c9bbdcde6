{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "SetCardStatusRequest", "$protected": true, "title": "SetCardStatusRequest", "description": "Request for setting card status", "type": "object", "required": ["identifier", "status"], "properties": {"identifier": {"type": "string", "description": "card identifier"}, "status": {"type": "string", "description": "card status", "enum": ["ISSUING", "ISSUED", "DELIVERED", "LEGAL_LOCKED", "PIN_LOCKED", "USER_LOCKED", "EXPIRED_LOCKED", "ACTIVE", "SUSPENDED", "LOST", "STOLEN", "REPLACEMENT", "REPLACED_LOCKED", "REPLACED_EXPIRED", "REPLACED_BLOCKED", "REPLACED_DELETED", "REPLACED", "EXPIRED", "BLOCKED", "DELETED"]}}}