{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "CreateCardTypeRequest", "$protected": true, "title": "CreateCardTypeRequest", "description": "Create Card Type Request", "type": "object", "required": ["code", "name", "description", "iin"], "properties": {"code": {"type": "string", "description": "card type code", "minLength": 1, "maxLength": 1}, "description": {"type": "string", "description": "card type description", "minLength": 1, "maxLength": 256}, "iin": {"type": "integer"}, "meta_data": {"type": "string", "description": "card type metadata"}, "name": {"type": "string", "description": "card type name", "minLength": 1, "maxLength": 256}}}