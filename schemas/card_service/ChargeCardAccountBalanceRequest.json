{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "ChargeCardAccountBalanceRequest", "$protected": true, "title": "ChargeCardAccountBalanceRequest", "description": "Request for charging card account balance", "type": "object", "required": ["primary_account_number", "amount", "asset"], "properties": {"primary_account_number": {"type": "string", "description": "card primary account number (16 digit card no)", "pattern": "^[0-9]{16,16}$", "minLength": 16, "maxLength": 16}, "amount": {"type": "number", "description": "amount to charge", "minimum": 0}, "asset": {"type": "string", "description": "asset to charge"}, "reference": {"type": "string", "description": "reference"}, "description": {"type": "string", "description": "description"}, "payment_id": {"type": "string", "description": "payment id"}}}