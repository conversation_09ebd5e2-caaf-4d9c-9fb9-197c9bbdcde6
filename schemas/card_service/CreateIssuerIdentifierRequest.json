{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "CreateIssuerIdentifierRequest", "$protected": true, "title": "CreateIssuerIdentifierRequest", "description": "Create Issuer Identifier Number Request", "type": "object", "required": ["iin", "status", "issuer_user_identifier", "issuer_name", "expire_date", "issuer_url"], "properties": {"iin": {"type": "string", "description": "issuer identifier number", "pattern": "^[0-9]{4,4}$", "minLength": 4, "maxLength": 4}, "status": {"type": "string", "description": "issuer identifier status", "enum": ["PENDING", "REJECTED", "ACTIVE", "INACTIVE", "DELETED", "BLOCKED", "SUSPENDED", "LEGAL_LOCKED"]}, "issuer_user_identifier": {"type": "string", "description": "issuer identifier number owner user identifier", "pattern": "^[A-Z0-9]{4,64}$"}, "issuer_name": {"type": "string", "description": "issuer name", "minLength": 1, "maxLength": 256}, "issuer_logo": {"type": "string"}, "issuer_url": {"type": "string"}, "meta_data": {"type": "string"}, "expire_date": {"type": "object", "properties": {"seconds": {"type": "integer"}, "nanos": {"type": "integer"}}}}, "additionalProperties": false}