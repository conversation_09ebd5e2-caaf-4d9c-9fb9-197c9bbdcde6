{"swagger": "2.0", "info": {"title": "Card Service API", "description": "Card Service API for managing user cards and related operations.", "version": "1.0.0", "contact": {"name": "Card Service API Support", "email": "<EMAIL>"}}, "tags": [{"name": "CardService"}], "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/v1/card/context_get_cards": {"get": {"summary": "Get user context data", "operationId": "CardService_ContextUserCards", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbContextUserCardsResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "tags": ["CardService"]}}, "/v1/card/validate_card": {"post": {"summary": "Validate card details", "operationId": "CardService_ValidateCard", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbValidateCardResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbValidateCardRequest"}}], "tags": ["CardService"]}}}, "definitions": {"pbCardPAN": {"type": "object", "properties": {"identifier": {"type": "string", "title": "unique external identifier for internal-external system identifier isolation"}, "user_identifier": {"type": "string", "title": "owner of card external identifier"}, "holder_name": {"type": "string", "title": "holder name"}, "status": {"type": "string", "title": "card status determinable value"}, "card_number": {"type": "string", "title": "card number: iin_code+account_number+luhn_digit"}, "cvv": {"type": "string", "title": "cvv"}, "expire_year": {"type": "string", "title": "expire year"}, "expire_month": {"type": "string", "title": "expire month"}, "expires_at": {"type": "string", "format": "date-time", "title": "expire date and time of card"}, "created_at": {"type": "string", "format": "date-time", "title": "creation time"}, "updated_at": {"type": "string", "format": "date-time", "title": "update time"}, "deleted_at": {"type": "string", "format": "date-time", "title": "deletion time"}}}, "pbContextUserCardsResponse": {"type": "object", "properties": {"error": {"type": "boolean", "description": "whether the request returns error or no."}, "message": {"type": "string", "title": "error message if occurred or success message"}, "cards": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/pbCardPAN"}, "title": "list of user cards"}}, "title": "Response for getting context user cards list"}, "pbValidateCardRequest": {"type": "object", "properties": {"card_number": {"type": "string", "title": "card number to validate"}, "cvv": {"type": "string", "title": "card holder name to validate"}, "expire_month": {"type": "string", "title": "card expiration month to validate"}, "expire_year": {"type": "string", "title": "card expiration year to validate"}}, "title": "Request for validating a card"}, "pbValidateCardResponse": {"type": "object", "properties": {"error": {"type": "boolean", "description": "whether the request returns error or no."}, "message": {"type": "string", "title": "error message if occurred or success message"}, "is_valid": {"type": "boolean", "title": "whether the card is valid or not"}, "card": {"$ref": "#/definitions/pbCardPAN", "title": "card details if the card is valid"}}, "title": "Response for validating a card"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}, "securityDefinitions": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Authentication token, prefixed by Bear<PERSON>: Bearer <token>", "name": "Authorization", "in": "header"}}, "security": [{"Bearer": []}]}