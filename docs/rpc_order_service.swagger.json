{"swagger": "2.0", "info": {"title": "Order Service API", "description": "Order Service API for managing user orders and related operations.", "version": "1.0.0", "contact": {"name": "Order Service API Support", "email": "<EMAIL>"}}, "tags": [{"name": "OrderService"}], "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/v1/orders/cancel/{reference}": {"post": {"summary": "Cancel Order", "operationId": "OrderService_CancelOrder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbOrderActionsResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "reference", "description": "reference for order", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/OrderServiceCancelOrderBody"}}], "tags": ["OrderService"]}}, "/v1/orders/context_user_orders/{page_size}/{page_offset}": {"get": {"summary": "Get context user orders list", "operationId": "OrderService_ContextUserOrders", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbContextUserOrdersResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "page_size", "description": "page size", "in": "path", "required": true, "type": "integer", "format": "int32"}, {"name": "page_offset", "description": "page offset", "in": "path", "required": true, "type": "integer", "format": "int32"}], "tags": ["OrderService"]}}, "/v1/orders/pay_order": {"post": {"summary": "Pay Order", "operationId": "OrderService_PayOrder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbPayOrderResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbPayOrderRequest"}}], "tags": ["OrderService"]}}, "/v1/orders/place_order": {"post": {"summary": "Place an order", "operationId": "OrderService_PlaceOrder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbOrderActionsResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbPlaceOrderRequest"}}], "tags": ["OrderService"]}}, "/v1/orders/{reference}": {"get": {"summary": "Get context user order by specified reference", "operationId": "OrderService_GetOrder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbOrderActionsResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "reference", "description": "reference for order", "in": "path", "required": true, "type": "string"}], "tags": ["OrderService"]}}}, "definitions": {"OrderServiceCancelOrderBody": {"type": "object", "title": "OrderAction Request"}, "pbContextUserOrdersResponse": {"type": "object", "properties": {"error": {"type": "boolean", "description": "whether the request returns error or no."}, "message": {"type": "string", "title": "error message if occurred or success message"}, "result_length": {"type": "integer", "format": "int32", "title": "result array length"}, "orders": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/pbOrder"}, "title": "list of user orders"}}, "title": "Response for getting context user orders list"}, "pbOrder": {"type": "object", "properties": {"identifier": {"type": "string", "title": "unique external identifier for internal-external system identifier isolation"}, "user_identifier": {"type": "string", "title": "customer user external identifier"}, "card_identifier": {"type": "string", "title": "card external identifier"}, "card_holder_name": {"type": "string", "title": "card holder name"}, "card_pan": {"type": "string", "title": "card pan number"}, "target_domain": {"type": "string", "title": "target domain for order"}, "status": {"type": "string", "title": "order status determinable value"}, "order_type": {"type": "string", "title": "order type"}, "order_amount": {"type": "string", "title": "order amount"}, "order_asset": {"type": "string", "title": "order asset"}, "order_description": {"type": "string", "title": "order description"}, "reference": {"type": "string", "title": "order reference to make it idempotent"}, "voucher_code": {"type": "string", "title": "voucher code for order"}, "created_at": {"type": "string", "format": "date-time", "title": "when order was created"}, "updated_at": {"type": "string", "format": "date-time", "title": "when order was updated"}, "deleted_at": {"type": "string", "format": "date-time", "title": "when order was deleted"}}, "description": "Order is the main object that represents an order in the system."}, "pbOrderActionsResponse": {"type": "object", "properties": {"error": {"type": "boolean", "description": "whether the request returns error or no."}, "message": {"type": "string", "title": "error message if occurred or success message"}, "order": {"$ref": "#/definitions/pbOrder", "title": "created order"}}, "title": "Response for placing an order"}, "pbPayOrderRequest": {"type": "object", "properties": {"reference": {"type": "string", "title": "order reference"}, "gateway": {"type": "string", "title": "gateway identifier"}}, "title": "PayOrderRequest"}, "pbPayOrderResponse": {"type": "object", "properties": {"error": {"type": "boolean", "description": "whether the request returns error or no."}, "message": {"type": "string", "title": "error message if occurred or success message"}, "url": {"type": "string", "title": "generated payment gateway url"}}, "title": "PayOrderResponse"}, "pbPlaceOrderRequest": {"type": "object", "properties": {"card_pan": {"type": "string", "title": "card pan to be charged"}, "amount": {"type": "string", "title": "amount to be charged"}, "asset": {"type": "string", "title": "asset to be charged"}, "reference": {"type": "string", "title": "reference for idempotency"}, "description": {"type": "string", "title": "description for order"}, "target_domain": {"type": "string", "title": "target domain for order"}, "voucher_code": {"type": "string", "title": "voucher code to be applied"}}, "title": "Request for placing an order"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}, "securityDefinitions": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Authentication token, prefixed by Bear<PERSON>: Bearer <token>", "name": "Authorization", "in": "header"}}, "security": [{"Bearer": []}]}