{"swagger": "2.0", "info": {"title": "Card Administration Service API", "description": "Card Administration Service API for managing user cards and related operations.", "version": "1.0.0", "contact": {"name": "Card Administration Service API Support", "email": "<EMAIL>"}}, "tags": [{"name": "CardAdministrationService"}], "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/v1/admin/card/charge": {"post": {"operationId": "CardAdministrationService_ChargeCardAccountBalance", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbChargeAccountBalanceResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbChargeCardAccountBalanceRequest"}}], "tags": ["CardAdministrationService"]}}, "/v1/admin/card/create": {"post": {"summary": "Create a new card for a user", "operationId": "CardAdministrationService_CreateCard", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbCardActionsTypeResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "description": "Request for creating a card", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbCreateCardRequest"}}], "tags": ["CardAdministrationService"]}}, "/v1/admin/card/set_meta_data": {"post": {"operationId": "CardAdministrationService_SetCardMetaData", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbCardActionsTypeResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbSetCardMetaDataRequest"}}], "tags": ["CardAdministrationService"]}}, "/v1/admin/card/set_pin": {"post": {"operationId": "CardAdministrationService_SetCardPIN", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbCardActionsTypeResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbSetCardPINRequest"}}], "tags": ["CardAdministrationService"]}}, "/v1/admin/card/set_status": {"post": {"operationId": "CardAdministrationService_SetCardStatus", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbCardActionsTypeResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbSetCardStatusRequest"}}], "tags": ["CardAdministrationService"]}}, "/v1/admin/card_type/create": {"post": {"operationId": "CardAdministrationService_CreateCardType", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbCardTypeActionsResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "description": "Request for creating a card type", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbCreateCardTypeRequest"}}], "tags": ["CardAdministrationService"]}}, "/v1/admin/card_type/update": {"post": {"operationId": "CardAdministrationService_UpdateCardType", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbCardTypeActionsResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbUpdateCardTypeRequest"}}], "tags": ["CardAdministrationService"]}}, "/v1/admin/issuer_identifier/create": {"post": {"summary": "Create a new issuer identifier", "operationId": "CardAdministrationService_CreateIssuerIdentifier", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbIssuerIdentifierActionsResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "description": "Request for creating an issuer identifier", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbCreateIssuerIdentifierRequest"}}], "tags": ["CardAdministrationService"]}}, "/v1/admin/issuer_identifier/update": {"post": {"summary": "Update an existing issuer identifier", "operationId": "CardAdministrationService_UpdateIssuerIdentifier", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbIssuerIdentifierActionsResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbUpdateIssuerIdentifierRequest"}}], "tags": ["CardAdministrationService"]}}}, "definitions": {"pbCard": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "identifier": {"type": "string", "title": "unique external identifier for internal-external system identifier isolation"}, "user_identifier": {"type": "string", "title": "owner of card external identifier"}, "status": {"type": "string", "title": "card status determinable value"}, "iin": {"type": "string", "format": "int64", "title": "card issuer identifier number id"}, "card_type": {"type": "string", "format": "int64", "title": "card type id"}, "iin_code": {"type": "string", "title": "card issuer identifier code number"}, "card_type_code": {"type": "string", "title": "card type code"}, "account_number": {"type": "string", "title": "card account number"}, "luhn_digit": {"type": "string", "title": "luhn digit"}, "cvv": {"type": "string", "title": "cvv"}, "pin_code": {"type": "string", "title": "pin code"}, "card_number": {"type": "string", "title": "card number: iin_code(4)+card_type(1)+account_number(10)+luhn_digit(1)"}, "expire_year": {"type": "string", "title": "expire year"}, "expire_month": {"type": "string", "title": "expire month"}, "meta_data": {"type": "string", "title": "meta data"}, "expires_at": {"type": "string", "format": "date-time", "title": "expire date and time of card"}, "created_at": {"type": "string", "format": "date-time", "title": "creation time"}, "updated_at": {"type": "string", "format": "date-time", "title": "update time"}, "deleted_at": {"type": "string", "format": "date-time", "title": "deletion time"}}, "title": "Card model contains 'System Master Data' for cards\nIt stores all the necessary information about each card, including its status, identifiers, and metadata.\nThe model is designed to handle various card statuses and includes fields for card details such as IIN, account number, CVV, pin code, card number, expiration date, and metadata.\nThe model also includes timestamps for creation, updates, and deletion, allowing for tracking the lifecycle of each card.\nThe model is indexed on various fields to optimize query performance and ensure efficient data retrieval.\nThe model structure is as follows: Card PAN details"}, "pbCardActionsTypeResponse": {"type": "object", "properties": {"error": {"type": "boolean", "description": "whether the request returns error or no."}, "message": {"type": "string", "title": "error message if occurred or success message"}, "card": {"$ref": "#/definitions/pbCard", "title": "created card"}}, "title": "Response for creating, updating, or deleting a card"}, "pbCardType": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "identifier": {"type": "string", "title": "unique external identifier for internal-external system identifier isolation"}, "code": {"type": "string", "title": "card type code"}, "name": {"type": "string", "title": "card type name"}, "description": {"type": "string", "title": "card type description"}, "meta_data": {"type": "string", "title": "card type metadata"}, "created_at": {"type": "string", "format": "date-time", "title": "creation time"}, "updated_at": {"type": "string", "format": "date-time", "title": "update time"}, "deleted_at": {"type": "string", "format": "date-time", "title": "deletion time"}}, "description": "CardType model contains 'System Master Data' for card types\nIt stores information about different card types, including their unique identifiers, codes, names, descriptions, and metadata.\nThe model is designed to manage the metadata associated with each card type, allowing for easy retrieval and updates.\nThe model includes fields for the card type's code, name, description, and additional metadata.\nIt also includes timestamps for creation, updates, and deletion, enabling tracking of the lifecycle of each card type record."}, "pbCardTypeActionsResponse": {"type": "object", "properties": {"error": {"type": "boolean", "description": "whether the request returns error or no."}, "message": {"type": "string", "title": "error message if occurred or success message"}, "card_type": {"$ref": "#/definitions/pbCardType", "title": "created card type information"}}, "title": "Response for creating, updating, or deleting a card type"}, "pbChargeAccountBalanceResponse": {"type": "object", "properties": {"error": {"type": "boolean", "description": "whether the request returns error or no."}, "message": {"type": "string", "title": "error message if occurred or success message"}, "transaction_id": {"type": "string", "title": "transaction id"}, "payment_id": {"type": "string", "title": "payment id"}, "reference": {"type": "string", "title": "reference"}, "description": {"type": "string", "title": "description"}, "amount": {"type": "string", "title": "amount"}, "asset": {"type": "string", "title": "asset"}}, "title": "Response for charging card account balance"}, "pbChargeCardAccountBalanceRequest": {"type": "object", "properties": {"primary_account_number": {"type": "string", "title": "card primary account number (16 digit card no)"}, "amount": {"type": "number", "format": "double", "title": "amount to charge"}, "asset": {"type": "string", "title": "asset to charge"}, "reference": {"type": "string", "title": "reference"}, "description": {"type": "string", "title": "description"}, "payment_id": {"type": "string", "title": "payment id"}}, "title": "Request for charging card account balance"}, "pbCreateCardRequest": {"type": "object", "properties": {"user_identifier": {"type": "string", "title": "user identifier to create card for"}, "status": {"type": "string", "title": "card status"}, "iin": {"type": "string", "format": "int64", "title": "card issuer identifier number id"}, "iin_code": {"type": "string", "title": "card issuer identifier code number"}, "account_number": {"type": "string", "title": "card account number"}, "luhn_digit": {"type": "string", "title": "luhn digit"}, "cvv": {"type": "string", "title": "cvv"}, "pin_code": {"type": "string", "title": "pin code"}, "card_number": {"type": "string", "title": "card number: iin_code+account_number+luhn_digit"}, "expire_year": {"type": "string", "title": "expire year"}, "expire_month": {"type": "string", "title": "expire month"}, "meta_data": {"type": "string", "title": "meta data"}, "expires_at": {"type": "string", "format": "date-time", "title": "expire date and time of card"}, "card_type": {"type": "string", "title": "card type"}}, "description": "Request for creating a card", "title": "@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ CARD @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@"}, "pbCreateCardTypeRequest": {"type": "object", "properties": {"code": {"type": "string", "title": "card type code"}, "name": {"type": "string", "title": "card type name"}, "description": {"type": "string", "title": "card type description"}, "meta_data": {"type": "string", "title": "card type metadata"}, "iin": {"type": "string", "format": "int64", "title": "issuer identifier id"}}, "description": "Request for creating a card type", "title": "@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ CARD TYPE @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@"}, "pbCreateIssuerIdentifierRequest": {"type": "object", "properties": {"iin": {"type": "string", "title": "iin number"}, "status": {"type": "string", "title": "iin status"}, "issuer_user_identifier": {"type": "string", "title": "issuer owner user identifier"}, "issuer_name": {"type": "string", "title": "issuer name"}, "issuer_logo": {"type": "string", "title": "issuer logo url"}, "issuer_url": {"type": "string", "title": "issuer website url"}, "meta_data": {"type": "string", "title": "metadata"}, "expire_date": {"type": "string", "format": "date-time", "title": "expire date"}}, "description": "Request for creating an issuer identifier", "title": "@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ ISSUER IDENTIFIER @@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@"}, "pbIssuerIdentifier": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "identifier": {"type": "string", "title": "unique external identifier for internal-external system identifier isolation"}, "iin": {"type": "string", "title": "iin number"}, "status": {"type": "string", "title": "iin status"}, "issuer_identifier": {"type": "string", "title": "issuer owner user identifier"}, "issuer_name": {"type": "string", "title": "issuer name"}, "issuer_logo": {"type": "string", "title": "issuer logo url"}, "issuer_url": {"type": "string", "title": "issuer website url"}, "meta_data": {"type": "string", "title": "metadata"}, "expire_date": {"type": "string", "format": "date-time", "title": "expire date"}, "created_at": {"type": "string", "format": "date-time", "title": "creation time"}, "updated_at": {"type": "string", "format": "date-time", "title": "update time"}, "deleted_at": {"type": "string", "format": "date-time", "title": "deletion time"}}, "title": "issuer_identifier contains 'System Master Data' for IIN (Issuer Identification Number) registry\nIt stores information about card issuers, including their unique identifiers, names, logos, and URLs.\nThe model is designed to manage the metadata associated with each issuer, allowing for easy retrieval and updates.\nThe model includes fields for the issuer's IIN, external identifier, name, logo URL, website URL, and additional metadata.\nIt also includes timestamps for creation, updates, and deletion, enabling tracking of the lifecycle of each issuer record.\nThe model is indexed on various fields to optimize query performance and ensure efficient data retrieval.\nThe model structure is as follows:"}, "pbIssuerIdentifierActionsResponse": {"type": "object", "properties": {"error": {"type": "boolean", "description": "whether the request returns error or no."}, "message": {"type": "string", "title": "error message if occurred or success message"}, "issuer_identifier": {"$ref": "#/definitions/pbIssuerIdentifier", "title": "created card issuer information"}}, "title": "Response for creating, updating, or deleting an issuer identifier"}, "pbSetCardMetaDataRequest": {"type": "object", "properties": {"identifier": {"type": "string", "title": "card identifier"}, "meta_data": {"type": "string", "title": "card meta data"}}, "title": "Request for setting card meta data"}, "pbSetCardPINRequest": {"type": "object", "properties": {"identifier": {"type": "string", "title": "card identifier"}, "pin_code": {"type": "string", "title": "card pin code"}}, "title": "Request for setting card pin code"}, "pbSetCardStatusRequest": {"type": "object", "properties": {"identifier": {"type": "string", "title": "card identifier"}, "status": {"type": "string", "title": "card status"}}, "title": "Request for setting card status"}, "pbUpdateCardTypeRequest": {"type": "object", "properties": {"code": {"type": "string", "title": "card type code"}, "name": {"type": "string", "title": "card type name"}, "description": {"type": "string", "title": "card type description"}, "meta_data": {"type": "string", "title": "card type metadata"}}, "title": "Request for updating a card type"}, "pbUpdateIssuerIdentifierRequest": {"type": "object", "properties": {"identifier": {"type": "string", "title": "identifier"}, "status": {"type": "string", "title": "iin status"}, "issuer_user_identifier": {"type": "string", "title": "issuer owner user identifier"}, "issuer_name": {"type": "string", "title": "issuer name"}, "issuer_logo": {"type": "string", "title": "issuer logo url"}, "issuer_url": {"type": "string", "title": "issuer website url"}, "meta_data": {"type": "string", "title": "metadata"}, "expire_date": {"type": "string", "format": "date-time", "title": "expire date"}}, "title": "Request for updating issuer identifier"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}, "securityDefinitions": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Authentication token, prefixed by Bear<PERSON>: Bearer <token>", "name": "Authorization", "in": "header"}}, "security": [{"Bearer": []}]}